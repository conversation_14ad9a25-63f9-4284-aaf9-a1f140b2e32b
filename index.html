<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Froyolabs - AI Consulting & Implementation</title>
  </head>
  <body>
    <div id="root"></div>

    <!-- Initialize Netlify Identity on the root page so tokens in the URL hash are processed -->
    <script src="https://identity.netlify.com/v1/netlify-identity-widget.js"></script>
    <script>
      (function initNetlifyIdentityOnRoot(){
        if (window.netlifyIdentity) {
          // Use the same configuration as admin page for consistency
          const PROD_IDENTITY_API = 'https://froyolabs.netlify.app/.netlify/identity';
          const isLocal = (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');
          const initOptions = isLocal ? { APIUrl: PROD_IDENTITY_API } : {};
          
          try { 
            netlifyIdentity.init(initOptions);
            console.log('[identity] Root page initialized with options:', initOptions);
          } catch(e) { 
            console.warn('[identity] Root page init failed:', e);
          }

          // Parse tokens that may appear in the URL hash (confirmation_token, recovery_token, token)
          const hash = window.location.hash.replace(/^#/, '');
          const params = new URLSearchParams(hash);
          const token = params.get('confirmation_token') || params.get('recovery_token') || params.get('token');

          if (token) {
            console.log('[identity] Token detected on root page:', token.substring(0, 20) + '...');
            
            // Always redirect to admin page for token processing instead of processing on root
            // This ensures consistent token handling with proper error management
            if (!window.location.pathname.startsWith('/admin')) {
              const target = '/admin/index.html#' + window.location.hash.replace(/^#/, '');
              console.log('[identity] token present on root — redirecting to admin for processing:', target);
              // Use replace so user doesn't get stuck in back history
              window.location.replace(target);
              return;
            }

            // If we're already on admin page, add error handling for token processing
            netlifyIdentity.on('error', (err) => {
              console.error('[identity] Root page token error:', err);
              const errorMsg = err && err.message ? err.message : 'Authentication failed';
              
              if (errorMsg.toLowerCase().includes('user not found')) {
                alert('User not found. Please verify the email address or request a new confirmation/recovery link.');
              } else if (errorMsg.toLowerCase().includes('invalid token') || errorMsg.toLowerCase().includes('expired')) {
                alert('This link is invalid or has expired. Please request a new confirmation/recovery link.');
              }
            });

            // Open the widget so it processes the token (signup/login modal). The widget will handle token flows.
            netlifyIdentity.on('init', user => {
              if (!user) netlifyIdentity.open();
            });
            try { netlifyIdentity.open(); } catch (e) { /* ignore */ }
          }
        }
      })();
    </script>

    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
