// FAQ data moved to a separate file for easy editing
// Edit this array to update FAQ questions and answers shown in the app

const faqs = [
  {
    q: "Do you only consult, or do you also build?",
    a: "We do both. From strategy and architecture to hands-on implementation, optimization, and launch.",
  },
  {
    q: "What is an AI inference pipeline?",
    a: "A production system that serves AI models with low latency and high throughput across GPUs/CPUs with autoscaling, observability, and cost controls.",
  },
  {
    q: "Can you turn my model into a SaaS?",
    a: "Yes. We design multi-tenant SaaS around your AI service: auth, billing, APIs/SDKs, dashboards, and DevOps.",
  },
  {
    q: "Which models and stacks do you support?",
    a: "OpenAI/GPT, Llama, Mistral, and domain-specific models. Stacks include Triton, KServe, Ray Serve, ONNX/TensorRT, and Kubernetes/serverless.",
  },
  {
    q: "Can you deploy on-prem for privacy?",
    a: "Absolutely. We deliver private, air‑gapped or VPC deployments with compliance-friendly architecture for regulated industries.",
  },
];

export default faqs;
