export const SITE_CONFIG = {
  name: "Froyolabs",
  domain: "https://froyolabs.ai",
  email: "<EMAIL>",
  phone: "******-123-4567",
  description: "End-to-end AI consulting and implementation: inference pipelines, ChatGPT/LLM integration, and SaaS around your AI service.",
  
  // SEO & Social
  twitterImage: "https://froyolabs.ai/twitter-image.png",
  ogImage: "https://froyolabs.ai/og-image.png",
  logo: "https://froyolabs.ai/logo.png",
  
  // Navigation
  navigation: [
    { id: "home", title: "Home", type: "page" },
    { id: "services", title: "Services", type: "scroll" },
    { id: "faq", title: "FAQ", type: "scroll" },
    { id: "blog", title: "Blog", type: "page" },
    { id: "contact", title: "Contact", type: "scroll" },
  ],
  
  // Services
  services: [
    {
      title: "AI Inference Pipeline Optimization",
      description: "End-to-end consulting and implementation for AI pipelines that deliver low latency, high scalability, and cost efficiency.",
      icon: "zap"
    },
    {
      title: "Private AI for Sensitive Industries",
      description: "Custom AI on your private infrastructure for legal, healthcare, and finance with data privacy and compliance.",
      icon: "shield"
    },
    {
      title: "Cross-Platform Apps with Rust & Tauri",
      description: "High-performance, secure, lightweight desktop applications for Windows, macOS, and Linux from a single codebase.",
      icon: "layers"
    },
    {
      title: "Intelligent Automation Agents",
      description: "Streamline workflows with AI agents, MCP servers, and integrations with n8n, Zapier, and Make.",
      icon: "bot"
    },
    {
      title: "SaaS for Your AI Service",
      description: "We design, build, and launch SaaS around your AI — multi-tenant architecture, auth, billing, APIs/SDKs, and UX.",
      icon: "server"
    }
  ],
  
  // FAQ
  faqs: [
    {
      question: "Do you only consult, or do you also build?",
      answer: "We do both. From strategy and architecture to hands-on implementation, optimization, and launch."
    },
    {
      question: "What is an AI inference pipeline?",
      answer: "A production system that serves AI models with low latency and high throughput across GPUs/CPUs with autoscaling, observability, and cost controls."
    },
    {
      question: "Can you turn my model into a SaaS?",
      answer: "Yes. We design multi-tenant SaaS around your AI service: auth, billing, APIs/SDKs, dashboards, and DevOps."
    },
    {
      question: "Which models and stacks do you support?",
      answer: "OpenAI/GPT, Llama, Mistral, and domain-specific models. Stacks include Triton, KServe, Ray Serve, ONNX/TensorRT, and Kubernetes/serverless."
    },
    {
      question: "Can you deploy on-prem for privacy?",
      answer: "Absolutely. We deliver private, air‑gapped or VPC deployments with compliance-friendly architecture for regulated industries."
    }
  ],
  
  // Blog posts
  blogPosts: [
    {
      title: "Why Private AI is the Future for Regulated Industries",
      category: "AI & Privacy",
      excerpt: "Why on‑prem and VPC LLMs matter for legal, healthcare, and finance.",
      date: "August 1, 2025",
      slug: "private-ai-regulated-industries"
    },
    {
      title: "Rust + Tauri: The Ultimate Stack for Modern Desktop Apps?",
      category: "Development",
      excerpt: "Rust performance meets Tauri's lightweight shell for secure cross‑platform apps.",
      date: "July 22, 2025",
      slug: "rust-tauri-desktop-apps"
    },
    {
      title: "Automating Your Business with n8n and AI Agents",
      category: "Automation",
      excerpt: "Connect systems and automate workflows with n8n + custom AI agents.",
      date: "July 5, 2025",
      slug: "business-automation-n8n-ai"
    }
  ]
};
