import React, { useMemo, useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, HelmetProvider } from "react-helmet-async";
import { BrainCircuit, ShieldCheck, Bot, ArrowRight, Mail, Menu, X, Rss, Phone, ServerCog, Zap, Youtube, Github, Linkedin } from "lucide-react";
import faqs from "./config/faqs";
import Blog from "./components/Blog";
import AuthTester from "./components/AuthTester";

// ============================
// Config — tweak these values
// ============================
const SITE = {
  name: "Froyolabs",
  domain: "https://froyolabs.ai",
  email: "<EMAIL>",
  twitterImage: "https://froyolabs.ai/twitter-image.png",
  ogImage: "https://froyolabs.ai/og-image.png",
  logo: "https://froyolabs.ai/logo.png",
};

// Reusable Button
const Button = ({ children, href, onClick, variant = "primary", className = "", target, rel }) => {
  const base =
    "inline-flex items-center justify-center font-semibold py-3 px-6 rounded-xl text-base transition-transform duration-200 hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-purple-400";
  const variants = {
    primary: "bg-purple-600 hover:bg-purple-700 text-white",
    ghost: "bg-transparent border border-gray-700 hover:border-purple-500 text-gray-200",
  };
  const cls = `${base} ${variants[variant]} ${className}`;
  if (href) {
    return (
      <a href={href} onClick={onClick} className={cls} target={target} rel={rel}>
        {children}
      </a>
    );
  }
  return (
    <button onClick={onClick} className={cls}>
      {children}
    </button>
  );
};

// Service Card
const ServiceCard = ({ icon, title, description }) => (
  <article className="bg-gray-800/50 p-8 rounded-xl shadow-lg hover:shadow-purple-500/20 border border-gray-700 hover:border-purple-500/50 transition-all duration-300 transform hover:-translate-y-1 text-center">
    <div className="flex justify-center mb-4" aria-hidden>
      {icon}
    </div>
    <h3 className="text-xl font-bold text-white mb-2">{title}</h3>
    <p className="text-gray-400 text-sm leading-relaxed">{description}</p>
  </article>
);

// Blog Card (stub/demo)
// const BlogCard = ({ title, category, excerpt, date }) => (
//   <article className="bg-gray-800/50 rounded-xl overflow-hidden border border-gray-700 hover:border-purple-500/50 transition-all duration-300 flex flex-col">
//     <div className="p-6 flex-1">
//       <p className="text-xs text-purple-400 font-semibold mb-2">{category}</p>
//       <h3 className="text-lg font-bold text-white mb-2">{title}</h3>
//       <p className="text-gray-400 text-sm">{excerpt}</p>
//     </div>
//     <div className="bg-gray-800 px-6 py-3 text-xs text-gray-500 flex items-center justify-between">
//       <span>{date}</span>
//       <a href="#" className="text-purple-400 hover:text-purple-300 font-semibold">Read More →</a>
//     </div>
//   </article>
// );

const Header = ({ onNav, active, isOpen, setOpen }) => {
  const links = [
    { id: "home", title: "Home", type: "page" },
    { id: "services", title: "Services", type: "scroll" },
    { id: "faq", title: "FAQ", type: "scroll" },
    { id: "blog", title: "Blog", type: "page" },
    { id: "contact", title: "Contact", type: "scroll" },
  ];
  return (
    <header className="sticky top-0 z-50 bg-gray-900/80 backdrop-blur-sm shadow-md shadow-purple-500/10">
      <nav className="container mx-auto px-6 py-4 flex justify-between items-center" aria-label="Primary">
        <a
          aria-label={`${SITE.name} home`}
          href="#"
          onClick={(e) => {
            e.preventDefault();
            onNav({ id: "home", type: "page" });
          }}
          className="text-2xl font-bold text-white flex items-center"
        >
          <BrainCircuit className="text-purple-400 mr-2 h-8 w-8" aria-hidden />
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-pink-500">
            {SITE.name}
          </span>
        </a>
        <div className="hidden md:flex items-center space-x-8">
          {links.map((l) => (
            <a
              key={l.id}
              href={`#${l.id}`}
              onClick={(e) => {
                e.preventDefault();
                onNav(l);
              }}
              className={`text-sm transition-colors ${active === l.id ? "text-purple-400 font-semibold" : "text-gray-300 hover:text-purple-300"}`}
            >
              {l.title}
            </a>
          ))}
          {/* Admin link for content management - only show in development or with admin parameter */}
          {(window.location.hostname === 'localhost' || window.location.search.includes('admin=true')) && (
            <a
              href="/admin"
              className="text-xs bg-purple-600/20 text-purple-300 px-2 py-1 rounded border border-purple-500/30 hover:bg-purple-600/30 transition-colors"
              title="Content Management"
            >
              Admin
            </a>
          )}
        </div>
        <Button
          href="https://cal.com/froyolabs/15min"
          target="_blank"
          rel="noopener noreferrer"
          className="hidden md:inline-flex"
        >
          Book a Call <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
        <div className="md:hidden">
          <button aria-label="Open menu" onClick={() => setOpen(!isOpen)} className="text-gray-200">
            {isOpen ? <X size={26} /> : <Menu size={26} />}
          </button>
        </div>
      </nav>
      {isOpen && (
        <div className="md:hidden bg-gray-900/95">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 flex flex-col items-center">
            {links.map((l) => (
              <a
                key={l.id}
                href={`#${l.id}`}
                onClick={(e) => {
                  e.preventDefault();
                  setOpen(false);
                  onNav(l);
                }}
                className={`block w-full text-center px-3 py-2 rounded-md text-base font-medium transition-colors ${
                  active === l.id ? "bg-purple-600 text-white" : "text-gray-300 hover:bg-gray-700 hover:text-white"
                }`}
              >
                {l.title}
              </a>
            ))}
            <Button
              href="https://cal.com/froyolabs/15min"
              target="_blank"
              rel="noopener noreferrer"
              onClick={() => {
                setOpen(false);
              }}
              className="mt-2 w-full"
            >
              Book a Call
            </Button>
          </div>
        </div>
      )}
    </header>
  );
};

const Footer = ({ onNav }) => (
  <footer className="bg-gray-900 border-t border-gray-800" role="contentinfo">
    <div className="container mx-auto px-6 py-6">
      <div className="flex flex-col md:flex-row justify-between items-center gap-4">
        <div className="mb-0 text-center md:text-left flex-1">
          <a
            href="#"
            onClick={(e) => {
              e.preventDefault();
              onNav({ id: "home", type: "page" });
            }}
            className="text-2xl font-bold text-white flex items-center"
            aria-label={`${SITE.name} home`}
          >
            <BrainCircuit className="text-purple-400 mr-2 h-8 w-8" aria-hidden />
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-pink-500">{SITE.name}</span>
          </a>
          <p className="mt-2 text-gray-400">Secure & Private AI Solutions.</p>

          {/* Copyright moved here (where contact email used to be) */}
          <div className="mt-2 text-xs text-gray-500">© {new Date().getFullYear()} {SITE.name}. All rights reserved.</div>
        </div>

        <div className="flex flex-col items-center md:items-end gap-2">
          <div className="flex space-x-6" aria-label="Social links">
            <a href="https://www.youtube.com/@FroyoLabs" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-purple-400" aria-label="FroyoLabs YouTube">
              <Youtube size={22} />
            </a>
            <a href="https://github.com/froyolabs" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-purple-400" aria-label="FroyoLabs GitHub">
              <Github size={22} />
            </a>
            <a href="https://www.linkedin.com/company/froyolabs/" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-purple-400" aria-label="FroyoLabs LinkedIn">
              <Linkedin size={22} />
            </a>
          </div>

          {/* Contact email placed below the social icons */}
          <a href={`mailto:${SITE.email}`} className="text-sm text-purple-400 hover:text-purple-300">{SITE.email}</a>
        </div>
      </div>
    </div>
  </footer>
);

// FAQ Section + Schema
const FAQ = () => {
  const faqSchema = useMemo(
    () => ({
      "@context": "https://schema.org",
      "@type": "FAQPage",
      mainEntity: faqs.map((f) => ({
        "@type": "Question",
        name: f.q,
        acceptedAnswer: { "@type": "Answer", text: f.a },
      })),
    }),
    []
  );

  return (
    <section id="faq" className="py-20 bg-gray-900">
      <div className="container mx-auto px-6">
        <div className="text-center mb-10">
          <h2 className="text-3xl md:text-4xl font-bold text-white">Frequently Asked Questions</h2>
          {/* Hidden per request: tagline removed */}
        </div>
        <div className="max-w-3xl mx-auto divide-y divide-gray-800">
          {faqs.map((f, i) => (
            <details key={i} className="group py-4">
              <summary className="cursor-pointer list-none text-left text-white font-medium flex items-center justify-between">
                <span>{f.q}</span>
                <span className="ml-4 text-gray-400 group-open:rotate-90 transition-transform">›</span>
              </summary>
              <p className="text-gray-400 mt-2 leading-relaxed">{f.a}</p>
            </details>
          ))}
        </div>
        {/* FAQ Schema for AI/SEO */}
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(faqSchema) }} />
      </div>
    </section>
  );
};

const Home = ({ onScrollToContact }) => {
  const services = [
    {
      icon: <Zap className="h-12 w-12 text-purple-400" />,
      title: "AI Inference Pipeline Optimization",
      description:
        "End-to-end consulting and implementation for AI pipelines that deliver low latency, high scalability, and cost efficiency.",
    },
    {
      icon: <ShieldCheck className="h-12 w-12 text-purple-400" />,
      title: "Private AI for Sensitive Industries",
      description:
        "Custom AI on your private infrastructure for legal, healthcare, and finance with data privacy and compliance.",
    },
    {
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="48"
          height="48"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="text-purple-400"
          aria-hidden
        >
          <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
        </svg>
      ),
      title: "Cross-Platform Apps with Rust & Tauri",
      description:
        "High-performance, secure, lightweight desktop applications for Windows, macOS, and Linux from a single codebase.",
    },
    {
      icon: <Bot className="h-12 w-12 text-purple-400" />,
      title: "Intelligent Automation Agents",
      description:
        "Streamline workflows with AI agents, MCP servers, and integrations with n8n, Zapier, and Make.",
    },
    {
      icon: <ServerCog className="h-12 w-12 text-purple-400" />,
      title: "SaaS for Your AI Service",
      description:
        "We design, build, and launch SaaS around your AI — multi-tenant architecture, auth, billing, APIs/SDKs, and UX.",
    },
  ];

  // ItemList schema for services (helps AI associate offerings)
  const servicesSchema = useMemo(
    () => ({
      "@context": "https://schema.org",
      "@type": "ItemList",
      itemListElement: services.map((s, idx) => ({
        "@type": "Service",
        position: idx + 1,
        name: s.title,
        description: s.description,
        serviceType: s.title,
        provider: { "@type": "Organization", name: SITE.name },
      })),
    }),
    []
  );

  return (
    <>
      {/* Hero */}
      <section id="home" className="relative py-20 md:py-28 text-center text-white">
        <div className="absolute inset-0 bg-grid-purple-500/10 [mask-image:linear-gradient(to_bottom,white_5%,transparent_100%)]" aria-hidden></div>
        <div className="absolute inset-0 bg-gradient-to-b from-gray-900 via-gray-900/80 to-gray-900" aria-hidden></div>
        <div className="container mx-auto px-6 relative">
          <h1 className="text-4xl md:text-6xl font-extrabold leading-[1.1] md:leading-[1.3] mb-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-pink-500">
            Scale AI. Ship Apps. Stay Private.
          </h1>
          <p className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            Whether it’s blazing-fast inference, SaaS for your AI, or lightweight Rust & Tauri desktop apps — Froyolabs delivers performance, scalability, security, and cost efficiency without compromise.
          </p>
          <div className="flex justify-center gap-4">
            <Button href="https://cal.com/froyolabs/15min" target="_blank" rel="noopener noreferrer">
              Book a Discovery Call <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
            <Button onClick={onScrollToContact} variant="ghost">
              Get In Touch <Mail className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </section>

      {/* Services */}
      <section id="services" className="py-18 md:py-20 bg-gray-900">
        <div className="container mx-auto px-6">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold text-white">Our Expertise</h2>
            <p className="text-purple-400 mt-2">Inference pipelines • Private AI • SaaS for AI • Cross-platform Apps (Rust + Tauri)</p>
          </div>
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {services.map((s, i) => (
              <ServiceCard key={i} {...s} />
            ))}
          </div>
        </div>
        {/* Services Schema */}
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(servicesSchema) }} />
      </section>
    </>
  );
};

// Inline Blog component removed — using src/components/Blog.jsx instead

const Contact = () => (
  <section id="contact" className="py-20 bg-gray-900">
    <div className="container mx-auto px-6">
      <div className="text-center mb-10">
        <h2 className="text-3xl md:text-4xl font-bold text-white">Contact Us</h2>
        <p className="text-purple-400 mt-2">Let's build something amazing together</p>
      </div>
      
      <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Contact Information */}
        <div className="bg-gray-800/50 p-8 rounded-xl border border-gray-700 text-center">
          <p className="text-gray-300 text-lg mb-6">
            Have a project in mind or want to learn more? Reach out to us.
          </p>
          <Button href={`mailto:${SITE.email}`}>
            Send us an Email <Mail className="ml-2 h-4 w-4" />
          </Button>
        </div>
        
        {/* Authentication Tester */}
        <div>
          <AuthTester />
        </div>
      </div>
    </div>
  </section>
);

// Recovery modal shown when a recovery_token is present in the URL hash
const RecoveryModal = ({ token, onClose }) => {
  const [probeError, setProbeError] = React.useState(null);
  const [authError, setAuthError] = React.useState(null);

  React.useEffect(() => {
    if (!token) return;
    console.debug('[recovery] RecoveryModal mounted with token:', token);

    if (typeof window === 'undefined') return;

    // If the identity widget is present, initialize it but only after probing the API
    if (window.netlifyIdentity) {
      const PROD_IDENTITY_API = 'https://froyolabs.netlify.app/.netlify/identity';
      const isLocal = (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');
      const initOptions = isLocal ? { APIUrl: PROD_IDENTITY_API } : {};

      // probe the API if we are explicitly pointing to a different host (local -> prod)
      const apiToProbe = initOptions && initOptions.APIUrl ? initOptions.APIUrl : null;

      const doInit = () => {
        try {
          window.netlifyIdentity.init(initOptions);
          console.debug('[recovery] netlifyIdentity.init called with', initOptions);
          
          // Add error handling for recovery token issues
          window.netlifyIdentity.on('error', (err) => {
            console.error('[recovery] Netlify Identity error:', err);
            const errorMsg = err && err.message ? err.message : 'Authentication failed';
            
            if (errorMsg.toLowerCase().includes('user not found')) {
              setAuthError('User not found. Please verify the email address or request a new password reset link.');
            } else if (errorMsg.toLowerCase().includes('invalid token') || errorMsg.toLowerCase().includes('expired')) {
              setAuthError('This recovery link is invalid or has expired. Please request a new password reset link.');
            } else if (errorMsg.toLowerCase().includes('network') || errorMsg.toLowerCase().includes('fetch')) {
              setAuthError('Network error. Please check your connection and try again.');
            } else {
              setAuthError(`Authentication error: ${errorMsg}`);
            }
          });
          
          // Clear auth error on successful operations
          window.netlifyIdentity.on('login', () => {
            setAuthError(null);
            console.log('[recovery] Login successful');
          });
          
          window.netlifyIdentity.on('signup', () => {
            setAuthError(null);
            console.log('[recovery] Signup successful');
          });
          
        } catch (e) {
          console.warn('[recovery] netlifyIdentity.init failed', e);
          setProbeError('Failed to initialize Netlify Identity widget. See console for details.');
        }
      };

      if (apiToProbe) {
        fetch(apiToProbe, { method: 'GET', mode: 'cors' })
          .then((res) => {
            if (!res.ok) {
              console.warn('[recovery] Identity API probe returned non-OK', res.status, apiToProbe);
              setProbeError(`Identity API responded with ${res.status}. Confirm Identity is enabled on the target site.`);
              return;
            }
            doInit();
          })
          .catch((err) => {
            console.warn('[recovery] Identity API probe failed', err);
            setProbeError('Unable to reach Identity API. Check network and Identity settings on Netlify.');
          });
      } else {
        // no probe needed — init normally
        doInit();
      }
    } else {
      console.debug('[recovery] netlifyIdentity widget not present on page');
      setProbeError('Netlify Identity widget is not loaded on this page. The reset UI requires the widget.');
    }
  }, [token]);

  if (!token) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-black/60" onClick={onClose} />
      <div className="relative bg-gray-800 rounded-xl p-6 max-w-md w-full border border-gray-700">
        <h3 className="text-lg font-bold text-white mb-2">Reset your password</h3>
        <p className="text-gray-400 mb-2">We detected a password recovery link. Click "Open Reset UI" to continue — the Netlify Identity modal will handle the reset.</p>
        <p className="text-xs text-gray-500 mb-4">Debug token: <span className="break-words">{token}</span></p>
        {probeError && <div className="mb-3 text-sm text-yellow-300">{probeError}</div>}
        {authError && <div className="mb-3 p-3 text-sm text-red-300 bg-red-900/20 border border-red-800 rounded">{authError}</div>}
        <div className="flex justify-end gap-2">
          <Button variant="ghost" onClick={onClose}>Close</Button>
          <Button onClick={() => {
            if (typeof window !== 'undefined' && window.netlifyIdentity && window.netlifyIdentity.open) {
              try {
                // Clear previous auth errors when attempting to open
                setAuthError(null);
                // Open the widget and let it process the token in the location.hash
                window.netlifyIdentity.open();
                // Keep the modal visible — the widget will open as an overlay. Do not auto-close here.
              } catch (e) {
                console.warn('[recovery] netlifyIdentity.open failed', e);
                setAuthError('Unable to open identity widget. See console for details.');
              }
            } else {
              setAuthError('Netlify Identity widget not loaded yet. Please try reloading the page.');
            }
          }}>Open Reset UI</Button>
        </div>
      </div>
    </div>
  );
};

// ============================
// Main App with AI/SEO schemas
// ============================
const Shell = () => {
  const [activePage, setActivePage] = useState("home");
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  // track a recovery token (if present in URL hash) so we can show a reset modal
  const [recoveryToken, setRecoveryToken] = useState(null);

  // Keep app state in sync with the browser URL (on mount and back/forward)
  useEffect(() => {
    // If a Netlify Identity token is present in the URL hash, wait for the identity widget
    // to initialize and process it before the app runs its routing/redirect logic. This
    // prevents the SPA from replacing the hash or redirecting away while the CMS modal
    // is opening (which was closing the modal after a second).
    const hasIdentityToken = !!(typeof window !== 'undefined' && window.location && /confirmation_token|recovery_token|token/.test(window.location.hash));

    // Global flag to let other parts of the app know we are processing an identity token
    if (typeof window !== 'undefined') {
      window.__identityTokenProcessing = !!hasIdentityToken;
      if (window.__identityTokenProcessing) console.log('[identity] token detected — delaying app routing/navigation');
    }

    function resolveLocation() {
      try {
        const path = window.location.pathname || '';
        const hash = (window.location.hash || '').replace('#', '');

        // Handle admin route - redirect to admin/index.html
        if (path === '/admin' || path === '/admin/') {
          window.location.href = '/admin/index.html';
          return;
        }

        // If the path or query/hash indicates the blog, show Blog
        if (/^\/blog(\/.*)?$/.test(path) || new URLSearchParams(window.location.search).get('page') === 'blog' || hash === 'blog') {
          setActivePage('blog');
          return;
        }

        // If there's a section hash, treat that as the active page so nav highlights correctly
        if (['home', 'services', 'faq', 'contact'].includes(hash)) {
          setActivePage(hash || 'home');
          setTimeout(() => document.getElementById(hash)?.scrollIntoView({ behavior: 'smooth' }), 100);
          return;
        }

        // Default to home
        setActivePage('home');
        window.scrollTo({ top: 0, behavior: 'smooth' });
      } catch (e) {
        // ignore (non-browser environments)
      }
    }

    if (hasIdentityToken && typeof window !== 'undefined' && window.netlifyIdentity && window.netlifyIdentity.on) {
      // Wait for Netlify Identity to initialize and handle the token, then run routing
      try {
        // Add error handling for identity token processing
        window.netlifyIdentity.on('error', (err) => {
          console.error('[identity] Token processing error:', err);
          const errorMsg = err && err.message ? err.message : 'Authentication failed';
          
          if (errorMsg.toLowerCase().includes('user not found')) {
            alert('User not found. Please verify the email address or request a new confirmation/recovery link.');
          } else if (errorMsg.toLowerCase().includes('invalid token') || errorMsg.toLowerCase().includes('expired')) {
            alert('This link is invalid or has expired. Please request a new confirmation/recovery link.');
          } else {
            console.warn('[identity] Authentication error:', errorMsg);
          }
          
          // Continue with routing even after error
          if (typeof window !== 'undefined') {
            window.__identityTokenProcessing = false;
          }
        });

        // When identity finishes init, clear the processing flag after a short grace period
        window.netlifyIdentity.on('init', () => {
          console.log('[identity] init event received');
          // give the widget a short moment to process the token/modal
          setTimeout(() => {
            resolveLocation();
            if (typeof window !== 'undefined') {
              window.__identityTokenProcessing = false;
              console.log('[identity] processing complete — resuming app routing/navigation');
            }
          }, 600);
        });

        // ensure init is called in case it hasn't been
        try { window.netlifyIdentity.init(); } catch (e) { /* ignore */ }

        // Fallback: if something goes wrong, ensure resolveLocation still runs after 3s
        const fallback = setTimeout(() => {
          resolveLocation();
          if (typeof window !== 'undefined') window.__identityTokenProcessing = false;
        }, 3000);
        // clear fallback if login/signup succeeds
        window.netlifyIdentity.on('login', () => { clearTimeout(fallback); if (typeof window !== 'undefined') window.__identityTokenProcessing = false; });
        window.netlifyIdentity.on('signup', () => { clearTimeout(fallback); if (typeof window !== 'undefined') window.__identityTokenProcessing = false; });

      } catch (e) {
        // if any error, just resolve immediately
        resolveLocation();
        if (typeof window !== 'undefined') window.__identityTokenProcessing = false;
      }
    } else if (hasIdentityToken && typeof window !== 'undefined' && !window.netlifyIdentity) {
      // If token exists but widget isn't available yet, wait a bit for it to load
      setTimeout(() => { resolveLocation(); if (typeof window !== 'undefined') window.__identityTokenProcessing = false; }, 800);
    } else {
      // No token — run routing immediately
      resolveLocation();
      if (typeof window !== 'undefined') window.__identityTokenProcessing = false;
    }

    const onPop = () => resolveLocation();
    window.addEventListener('popstate', onPop);
    return () => window.removeEventListener('popstate', onPop);
  }, []);

  // Separate effect: parse the hash for a recovery_token and show the modal on the home page
  useEffect(() => {
    try {
      if (typeof window === 'undefined') return;
      const raw = window.location.hash || '';
      console.debug('[recovery] raw hash:', raw);
      
      // Parse more robustly: handle both URLSearchParams format and simple key=value format
      const hash = raw.replace(/^#\/?/, '');
      console.debug('[recovery] normalized hash:', hash);
      
      // Try URLSearchParams first
      try {
        const params = new URLSearchParams(hash);
        const recoveryTokenFromParams = params.get('recovery_token');
        if (recoveryTokenFromParams) {
          const decoded = decodeURIComponent(recoveryTokenFromParams);
          console.debug('[recovery] found recovery_token via URLSearchParams:', decoded);
          setRecoveryToken(decoded);
          return;
        }
      } catch (e) {
        console.debug('[recovery] URLSearchParams parsing failed, trying manual parsing');
      }
      
      // Fallback to manual parsing for edge cases
      const parts = hash.split('&');
      for (const p of parts) {
        const [k, v] = p.split('=');
        if (k === 'recovery_token' && v) {
          const decoded = decodeURIComponent(v);
          console.debug('[recovery] found recovery_token via manual parsing:', decoded);
          setRecoveryToken(decoded);
          return;
        }
      }
    } catch (e) {
      console.warn('[recovery] Error parsing hash for recovery token:', e);
    }
  }, []);

  const onNav = (link) => {
    // If identity token is being processed, block navigation to avoid closing widget
    if (typeof window !== 'undefined' && window.__identityTokenProcessing) {
      console.log('[nav] navigation blocked while identity token is being processed');
      return;
    }

    setIsMenuOpen(false);
    if (link.type === "page") {
      // page navigation (home, blog)
      setActivePage(link.id);
      try {
        const url = link.id === 'home' ? '/' : `/${link.id}`;
        window.history.pushState({}, '', url);
      } catch (e) {
        // ignore
      }
      window.scrollTo({ top: 0, behavior: "smooth" });
    } else if (link.type === "scroll") {
      // section navigation (services, faq, contact)
      // set activePage to the section id so the nav highlights the current section
      setActivePage(link.id);
      try {
        // ensure we reset path to root and set a hash (avoid keeping /blog in the URL)
        window.history.pushState({}, '', `/#${link.id}`);
      } catch (e) {
        // ignore
      }
      setTimeout(() => {
        document.getElementById(link.id)?.scrollIntoView({ behavior: "smooth" });
      }, 100);
    }
  };

  const onScrollToContact = () => {
    if (typeof window !== 'undefined' && window.__identityTokenProcessing) {
      console.log('[nav] contact scroll blocked while identity token is being processed');
      return;
    }
    // navigate to contact section and update URL
    setActivePage("contact");
    try { window.history.pushState({}, '', '/#contact'); } catch (e) {}
    setTimeout(() => document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" }), 100);
  };

  // ======= Schema.org blocks =======
  const organizationSchema = useMemo(
    () => ({
      "@context": "https://schema.org",
      "@type": "Organization",
      name: SITE.name,
      url: SITE.domain,
      logo: SITE.logo,
      contactPoint: {
        "@type": "ContactPoint",
        email: SITE.email,
        contactType: "sales",
        availableLanguage: ["English"],
      },
      sameAs: [
        // Add your real social links
      ],
    }),
    []
  );

  const websiteSchema = useMemo(
    () => ({
      "@context": "https://schema.org",
      "@type": "WebSite",
      name: SITE.name,
      url: SITE.domain,
      potentialAction: {
        "@type": "SearchAction",
        target: `${SITE.domain}/search?q={query}`,
        "query-input": "required name=query",
      },
    }),
    []
  );

  const breadcrumbsSchema = useMemo(
    () => ({
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      itemListElement: [
        { "@type": "ListItem", position: 1, name: "Home", item: `${SITE.domain}/` },
        { "@type": "ListItem", position: 2, name: activePage === "blog" ? "Blog" : "Services", item: `${SITE.domain}/${activePage}` },
      ],
    }),
    [activePage]
  );

  return (
    <div className="bg-gray-900 text-gray-100 font-sans antialiased min-h-screen flex flex-col">
      <Header onNav={onNav} active={activePage} isOpen={isMenuOpen} setOpen={setIsMenuOpen} />

      <main className="flex-1">
        {/* Routes (simple) */}
        {activePage === "blog" ? (
          <Blog />
        ) : (
          <>
            <Home onScrollToContact={onScrollToContact} />
            <FAQ />
            <Contact />
          </>
        )}
      </main>

      <Footer onNav={onNav} />

      {/* Recovery modal — shown when a recovery_token exists in the URL hash */}
      {recoveryToken && <RecoveryModal token={recoveryToken} onClose={() => setRecoveryToken(null)} />}

      {/* Global Schemas for AI/SEO */}
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationSchema) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(websiteSchema) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbsSchema) }} />
    </div>
  );
};

export default function App() {
  // Head tags for AI/SEO + social summarizers
  return (
    <HelmetProvider>
      <Helmet>
        <html lang="en" />
        <title>AI Consultancy | Inference Pipelines & SaaS for Your AI</title>
        <meta
          name="description"
          content="End-to-end AI consulting and implementation: inference pipelines, ChatGPT/LLM integration, and SaaS around your AI service. Low latency, scalable, and cost-efficient."
        />
        <meta
          name="keywords"
          content="AI consultancy, AI inference pipelines, SaaS for AI, ChatGPT integration, LLM deployment, MLOps, Kubernetes, Triton, KServe, TensorRT, ONNX"
        />
        <meta name="author" content={SITE.name} />

        {/* Open Graph */}
        <meta property="og:title" content="AI Consultancy | Inference Pipelines & SaaS for Your AI" />
        <meta
          property="og:description"
          content="We design, build, and scale AI services — inference pipelines, private AI, and SaaS for your AI."
        />
        <meta property="og:type" content="website" />
        <meta property="og:url" content={SITE.domain} />
        <meta property="og:image" content={SITE.ogImage} />

        {/* Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="AI Consultancy | Inference Pipelines & SaaS for Your AI" />
        <meta name="twitter:description" content="End‑to‑end AI consulting & implementation for scalable, low‑latency AI." />
        <meta name="twitter:image" content={SITE.twitterImage} />

        {/* Prefer short, canonical URL */}
        <link rel="canonical" href={SITE.domain} />

        {/* Favicon (optional) */}
        {/* <link rel="icon" href="/favicon.ico" /> */}
      </Helmet>
      <Shell />
    </HelmetProvider>
  );
}
