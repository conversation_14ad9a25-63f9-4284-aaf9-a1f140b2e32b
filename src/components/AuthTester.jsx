import React, { useState, useEffect } from 'react';

const AuthTester = () => {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState('');
  const [statusType, setStatusType] = useState('');
  const [currentUser, setCurrentUser] = useState(null);
  const [isIdentityLoaded, setIsIdentityLoaded] = useState(false);

  useEffect(() => {
    // Check if Netlify Identity is loaded and set up listeners
    const checkIdentity = () => {
      if (typeof window !== 'undefined' && window.netlifyIdentity) {
        setIsIdentityLoaded(true);
        
        // Initialize if not already done
        try {
          const PROD_IDENTITY_API = 'https://froyolabs.netlify.app/.netlify/identity';
          const isLocal = (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');
          const initOptions = isLocal ? { APIUrl: PROD_IDENTITY_API } : {};
          
          window.netlifyIdentity.init(initOptions);
          console.log('[auth-tester] Identity initialized with options:', initOptions);
        } catch (e) {
          console.warn('[auth-tester] Identity init error:', e);
        }

        // Set current user
        const user = window.netlifyIdentity.currentUser();
        setCurrentUser(user);

        // Set up event listeners
        window.netlifyIdentity.on('login', (user) => {
          setCurrentUser(user);
          setStatus(`✅ Logged in as ${user.email}`);
          setStatusType('success');
        });

        window.netlifyIdentity.on('logout', () => {
          setCurrentUser(null);
          setStatus('👋 Logged out successfully');
          setStatusType('info');
        });

        window.netlifyIdentity.on('error', (err) => {
          console.error('[auth-tester] Identity error:', err);
          setStatus(`❌ Error: ${err.message}`);
          setStatusType('error');
        });
      } else {
        // Check again in a moment
        setTimeout(checkIdentity, 500);
      }
    };

    checkIdentity();
  }, []);

  const showStatus = (message, type) => {
    setStatus(message);
    setStatusType(type);
    // Clear status after 5 seconds for non-error messages
    if (type !== 'error') {
      setTimeout(() => setStatus(''), 5000);
    }
  };

  const handlePasswordReset = async () => {
    if (!email) {
      showStatus('Please enter an email address', 'error');
      return;
    }

    if (!isIdentityLoaded) {
      showStatus('Netlify Identity not loaded yet', 'error');
      return;
    }

    try {
      showStatus('🔄 Sending password reset email...', 'info');
      
      await window.netlifyIdentity.gotrue.requestPasswordRecovery(email);
      showStatus(`📧 Password reset email sent to ${email}! Check your inbox.`, 'success');
      console.log('[auth-tester] Password reset email sent to:', email);
    } catch (error) {
      console.error('[auth-tester] Password reset error:', error);
      
      let errorMsg = 'Failed to send reset email';
      if (error.message) {
        if (error.message.toLowerCase().includes('user not found')) {
          errorMsg = `❌ No user found with email "${email}". Please verify the email address or create a new account.`;
        } else if (error.message.toLowerCase().includes('rate limit')) {
          errorMsg = '⏱️ Too many requests. Please wait a few minutes before trying again.';
        } else {
          errorMsg = `❌ Error: ${error.message}`;
        }
      }
      
      showStatus(errorMsg, 'error');
    }
  };

  const openAuthWidget = (mode = 'login') => {
    if (isIdentityLoaded) {
      try {
        window.netlifyIdentity.open(mode);
      } catch (e) {
        window.netlifyIdentity.open();
      }
    } else {
      showStatus('Netlify Identity not loaded yet', 'error');
    }
  };

  const handleLogout = () => {
    if (isIdentityLoaded && currentUser) {
      window.netlifyIdentity.logout();
    }
  };

  const getStatusStyle = () => {
    switch (statusType) {
      case 'success':
        return 'bg-green-900/20 text-green-300 border-green-800';
      case 'error':
        return 'bg-red-900/20 text-red-300 border-red-800';
      case 'info':
        return 'bg-blue-900/20 text-blue-300 border-blue-800';
      default:
        return 'bg-gray-800/50 text-gray-300 border-gray-700';
    }
  };

  return (
    <div className="bg-gray-800/50 p-6 rounded-xl border border-gray-700 max-w-md mx-auto">
      <h3 className="text-xl font-bold text-white mb-4">🔐 Authentication Tester</h3>
      
      {/* Current Status */}
      <div className="mb-4 p-3 rounded-lg bg-gray-900/50 border border-gray-700">
        <div className="text-sm text-gray-400 mb-1">Status:</div>
        <div className="text-white">
          {isIdentityLoaded ? (
            currentUser ? (
              <span className="text-green-400">✅ Logged in as {currentUser.email}</span>
            ) : (
              <span className="text-yellow-400">⚪ Not logged in</span>
            )
          ) : (
            <span className="text-red-400">❌ Identity widget not loaded</span>
          )}
        </div>
      </div>

      {/* Password Reset Test */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Test Password Reset
        </label>
        <input
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="Enter email address"
          className="w-full px-3 py-2 bg-gray-900 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-500 focus:outline-none"
        />
        <button
          onClick={handlePasswordReset}
          className="w-full mt-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors"
        >
          Send Reset Email
        </button>
      </div>

      {/* Auth Actions */}
      <div className="flex gap-2 mb-4">
        {currentUser ? (
          <button
            onClick={handleLogout}
            className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors"
          >
            Logout
          </button>
        ) : (
          <>
            <button
              onClick={() => openAuthWidget('login')}
              className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
            >
              Login
            </button>
            <button
              onClick={() => openAuthWidget('signup')}
              className="flex-1 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium transition-colors"
            >
              Sign Up
            </button>
          </>
        )}
      </div>

      {/* Status Messages */}
      {status && (
        <div className={`p-3 rounded-lg border text-sm ${getStatusStyle()}`}>
          {status}
        </div>
      )}

      {/* Debug Links */}
      <div className="mt-4 pt-4 border-t border-gray-700">
        <div className="text-xs text-gray-400 mb-2">Debug Tools:</div>
        <div className="flex gap-2 text-xs">
          <a
            href="/admin/"
            target="_blank"
            className="text-purple-400 hover:text-purple-300 underline"
          >
            CMS Admin
          </a>
          <a
            href="/debug-recovery.html"
            target="_blank"
            className="text-purple-400 hover:text-purple-300 underline"
          >
            Recovery Debug
          </a>
        </div>
      </div>
    </div>
  );
};

export default AuthTester;
