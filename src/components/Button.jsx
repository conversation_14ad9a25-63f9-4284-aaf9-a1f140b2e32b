import React from "react";

export const Button = ({ children, href, onClick, variant = "primary", className = "" }) => {
  const base =
    "inline-flex items-center justify-center font-semibold py-3 px-6 rounded-xl text-base transition-transform duration-200 hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-purple-400";
  
  const variants = {
    primary: "bg-purple-600 hover:bg-purple-700 text-white",
    ghost: "bg-transparent border border-gray-700 hover:border-purple-500 text-gray-200",
    secondary: "bg-gray-700 hover:bg-gray-600 text-white",
  };
  
  const cls = `${base} ${variants[variant]} ${className}`;
  
  if (href) {
    return (
      <a href={href} onClick={onClick} className={cls}>
        {children}
      </a>
    );
  }
  
  return (
    <button onClick={onClick} className={cls}>
      {children}
    </button>
  );
};
