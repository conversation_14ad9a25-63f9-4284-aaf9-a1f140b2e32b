import { useEffect, useState } from "react";
import ReactMarkdown from "react-markdown";
import matter from "gray-matter";

// Load markdown files dynamically so Netlify CMS can add/remove posts without changing code
// Use a relative glob from this file to the src/content/posts directory so Vite resolves correctly
const postsGlob = import.meta.glob('../content/posts/*.md', { as: 'raw' });
// Fallback absolute-style glob for some environments (points to /src/content/posts)
const postsGlobAlt = import.meta.glob('/src/content/posts/*.md', { as: 'raw' });
// NOTE: intentionally do not load /docs here — we only want blog posts from content/posts

// Parse markdown content with frontmatter support
function parsePost(raw, filename) {
  try {
    // Try to parse frontmatter first
    const { data: frontmatter, content } = matter(raw);

    if (frontmatter && Object.keys(frontmatter).length > 0) {
      // Netlify CMS format with frontmatter
      return {
        id: filename.replace(/\.md$/i, ''),
        title: frontmatter.title || filename,
        category: frontmatter.category || '',
        date: frontmatter.date ? 
          (typeof frontmatter.date === 'string' ? frontmatter.date : frontmatter.date.toLocaleDateString("en-US", {
            year: "numeric",
            month: "long", 
            day: "numeric"
          })) : '',
        excerpt: frontmatter.excerpt || createExcerpt(content),
        body: content,
        featured_image: frontmatter.featured_image || '',
        tags: frontmatter.tags || [],
        draft: frontmatter.draft || false
      };
    } else {
      // Legacy format (current posts)
      return parseLegacyPost(raw, filename);
    }
  } catch (error) {
    console.warn(`Error parsing post ${filename}:`, error);
    return parseLegacyPost(raw, filename);
  }
}

// Parse legacy format posts (your current format)
function parseLegacyPost(raw, filename) {
  const lines = raw.split("\n");
  let title = "";
  let category = "";
  let date = "";

  // Extract title (first heading)
  for (const line of lines) {
    const t = line.trim();
    if (t.startsWith("# ")) {
      title = t.replace(/^#\s+/, "").trim();
      break;
    }
  }

  // Extract metadata
  const metaMatchCategory = raw.match(/\*\*Category:\*\*\s*(.*)/i);
  const metaMatchDate = raw.match(/\*\*Date:\*\*\s*(.*)/i);
  if (metaMatchCategory) category = metaMatchCategory[1].trim();
  if (metaMatchDate) date = metaMatchDate[1].trim();

  // Get body content (skip title and metadata)
  const bodyStartIndex = (() => {
    let idx = 0;
    if (lines[0] && lines[0].trim().startsWith("# ")) idx = 1;
    while (idx < lines.length && (lines[idx].trim() === "" || lines[idx].trim().startsWith("**"))) idx++;
    return idx;
  })();

  const bodyLines = lines.slice(bodyStartIndex);
  const body = bodyLines.join("\n");

  return {
    id: filename.replace(/\.md$/i, ''),
    title: title || filename,
    category,
    date,
    excerpt: createExcerpt(body),
    body,
    featured_image: '',
    tags: [],
    draft: false
  };
}

// Create excerpt from content
function createExcerpt(body) {
  // Create excerpt
  const paragraphs = body
    .split(/\n\s*\n/)
    .map((p) => p.trim())
    .filter(Boolean)
    .map(p =>
      p.replace(/\*\*Category:\*\*.*$/im, '').replace(/\*\*Date:\*\*.*$/im, '').trim()
    );

  let excerpt = paragraphs.length ? paragraphs[0] : "";
  if (excerpt.length > 220) excerpt = excerpt.slice(0, 220).trim() + "…";
  return excerpt;
}

// Simple slugify function
function slugify(text) {
  return String(text)
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, "")
    .trim()
    .replace(/\s+/g, "-");
}

// Deterministic token generator (djb2 -> base36) to avoid exposing filenames in the URL
function generateToken(str) {
  let h = 5381;
  for (let i = 0; i < str.length; i++) {
    h = (h * 33) ^ str.charCodeAt(i);
  }
  // >>> 0 to force unsigned, then base36 for compact token
  return (h >>> 0).toString(36).slice(-8);
}

// Extract headings from markdown
function extractHeadings(markdown) {
  const lines = markdown.split('\n');
  const headings = [];
  const usedIds = new Set();
  
  for (const line of lines) {
    const match = line.match(/^(#{1,4})\s+(.*)$/);
    if (match) {
      const level = match[1].length;
      const text = match[2].trim();
      let id = slugify(text);
      
      // Make sure ID is unique
      let counter = 1;
      let finalId = id;
      while (usedIds.has(finalId)) {
        finalId = `${id}-${counter}`;
        counter++;
      }
      usedIds.add(finalId);
      
      headings.push({ id: finalId, level, text });
    }
  }
  return headings;
}

export default function Blog() {
  const [posts, setPosts] = useState([]);
  const [selectedPost, setSelectedPost] = useState(null);
  const [headings, setHeadings] = useState([]);
  const [activeId, setActiveId] = useState("");
  const [showBackToTop, setShowBackToTop] = useState(false);
  const [tocOpen, setTocOpen] = useState(true);
  const [collapsed, setCollapsed] = useState(new Set());

  // Build a nested tree from flat headings list
  function buildTOCTree(headings) {
    const root = [];
    const stack = [];

    for (const h of headings) {
      const node = { ...h, children: [] };

      while (stack.length && stack[stack.length - 1].level >= node.level) {
        stack.pop();
      }

      if (stack.length === 0) {
        root.push(node);
      } else {
        stack[stack.length - 1].children.push(node);
      }

      stack.push(node);
    }

    return root;
  }

  function toggleCollapse(id) {
    setCollapsed((prev) => {
      const next = new Set(prev);
      if (next.has(id)) next.delete(id);
      else next.add(id);
      return next;
    });
  }

  // Load posts on mount
  useEffect(() => {
    const loadPosts = async () => {
      try {
        // Merge possible glob sources to be robust across setups
        const globs = { ...postsGlob, ...postsGlobAlt };

        if (Object.keys(globs).length === 0) {
          console.warn('No markdown files found by import.meta.glob for content/posts. Check glob path.');
        }

        // Deduplicate by filename to avoid loading the same file twice when multiple
        // glob patterns (relative + absolute) both match the same file.
        const uniqueByName = {};
        for (const [path, loader] of Object.entries(globs)) {
          const name = path.split('/').pop();
          if (!uniqueByName[name]) uniqueByName[name] = { path, loader };
        }

        // Load all unique files in parallel
        const entries = await Promise.all(
          Object.values(uniqueByName).map(async ({ path, loader }) => {
            try {
              const content = await loader();
              const name = path.split('/').pop();
              return { content, name, path };
            } catch (err) {
              console.warn(`Failed to load ${path}:`, err);
              return null;
            }
          })
        );

        const filesRaw = entries.filter(Boolean);

        // Deduplicate posts by their id (derived from filename) to avoid duplicates
        // caused by overlapping glob patterns returning the same file via different paths.
        const postsById = new Map();
        for (const file of filesRaw) {
          try {
            const post = parsePost(file.content, file.name);
            if (post.draft) continue;

            if (!postsById.has(post.id)) {
              postsById.set(post.id, { post, sourcePath: file.path });
            } else {
              const existing = postsById.get(post.id).sourcePath;
              console.warn(
                `Duplicate post id "${post.id}" found at ${file.path}. Already loaded from ${existing}; ignoring duplicate.`
              );
            }
          } catch (error) {
            console.warn(`Failed to parse ${file.name}:`, error);
          }
        }

        const loadedPosts = Array.from(postsById.values()).map((v) => v.post);

        // Sort and add tokens
        const files = loadedPosts
          .sort((a, b) => (a.id < b.id ? 1 : -1))
          .map((f) => ({ ...f, token: generateToken(f.id) }));

        setPosts(files);

        // If the user navigated directly to /blog/:token (or legacy filename), open that post
        try {
          const path = window.location.pathname || '';
          const match = path.match(/^\/blog\/(.+)$/);
          if (match) {
            const param = match[1];
            const found = files.find((f) => f.token === param || f.id === param);
            if (found) {
              // Defer to let state settle
              setTimeout(() => openPost(found), 20);
            }
          }
        } catch (e) {
          // ignore (non-browser)
        }
      } catch (error) {
        console.error('Error loading posts:', error);
        setPosts([]);
      }
    };

    loadPosts();
  }, []);

  // Keep browser navigation in sync (back/forward)
  useEffect(() => {
    function handlePop() {
      try {
        const path = window.location.pathname || '';
        const match = path.match(/^\/blog\/(.+)$/);
        if (match) {
          const param = match[1];
          const found = posts.find((p) => p.token === param || p.id === param);
          if (found) {
            // Only open if not already showing
            if (!selectedPost || selectedPost.id !== found.id) {
              openPost(found);
            }
            return;
          }
        }
        // If path is /blog or anything else, ensure list is shown
        if (path === '/blog' || path === '/' || !path.startsWith('/blog/')) {
          if (selectedPost) closePost();
        }
      } catch (e) {
        // ignore
      }
    }

    window.addEventListener('popstate', handlePop);
    return () => window.removeEventListener('popstate', handlePop);
  }, [posts, selectedPost]);

  // Open a post
  function openPost(post) {
    const postHeadings = extractHeadings(post.body);
    setHeadings(postHeadings);
    setSelectedPost(post);
    setActiveId(postHeadings[0]?.id || "");
    
    // Scroll to top after a brief delay
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' });
      setShowBackToTop(false);
    }, 100);
  }

  // Close post and go back to list
  function closePost() {
    setSelectedPost(null);
    setHeadings([]);
    setActiveId("");
    setShowBackToTop(false);

    // Update address bar to the blog listing (replace current /blog/:id)
    try {
      const path = window.location.pathname || '';
      if (path.startsWith('/blog/')) {
        window.history.replaceState(null, '', '/blog');
      }
    } catch (e) {
      // ignore
    }
    
    // Scroll to top when going back to blog list
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }, 100);
  }

  // Scroll to a specific heading
  function scrollToHeading(headingId) {
    const element = document.getElementById(headingId);
    if (element) {
      const header = document.querySelector('.sticky');
      const offset = header ? header.offsetHeight + 20 : 20;
      const elementTop = element.offsetTop - offset;
      
      window.scrollTo({ top: elementTop, behavior: 'smooth' });
      
      setActiveId(headingId);
      setTimeout(() => setActiveId(headingId), 800);
    }
  }

  // Scroll to top of page
  function scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
    setActiveId("");
    setShowBackToTop(false);
  }

  // Track which heading is currently visible
  useEffect(() => {
    if (!selectedPost || headings.length === 0) return;

    function updateActiveHeading() {
      const header = document.querySelector('.sticky');
      const offset = header ? header.offsetHeight + 100 : 100;
      
      let foundActive = false;
      
      for (let i = headings.length - 1; i >= 0; i--) {
        const element = document.getElementById(headings[i].id);
        if (element && element.offsetTop <= window.scrollY + offset) {
          setActiveId(headings[i].id);
          foundActive = true;
          break;
        }
      }
      
      if (window.scrollY < 100) {
        setActiveId("");
        setShowBackToTop(false);
      } else {
        setShowBackToTop(true);
      }
    }

    let timeoutId;
    function debouncedUpdate() {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(updateActiveHeading, 50);
    }

    window.addEventListener('scroll', debouncedUpdate);
    return () => {
      window.removeEventListener('scroll', debouncedUpdate);
      clearTimeout(timeoutId);
    };
  }, [selectedPost, headings]);

  // Show individual post
  if (selectedPost) {
    return (
      <div className="min-h-screen bg-gray-900">
        {/* Sticky header */}
        <div className="bg-gray-900/80 backdrop-blur-sm border-b border-gray-800 sticky top-0 z-10">
          <div className="container mx-auto px-6 py-4">
            <button
              onClick={closePost}
              className="inline-flex items-center text-purple-400 hover:text-purple-300 font-medium"
            >
              ← Back to Blog
            </button>
          </div>
        </div>

        <div className="container mx-auto px-6 py-12 max-w-6xl">
          <div className="flex gap-8">
            {/* Table of Contents */}
            <nav className="hidden lg:block w-64 shrink-0 sticky top-24 h-fit">
              <div className="bg-gray-900/40 p-4 rounded-xl border border-gray-800">
                <div className="flex items-center justify-between mb-3">
                  <h4
                    className={`text-base font-semibold cursor-pointer transition-colors transform transition-transform duration-150 hover:scale-105 ${
                      showBackToTop ? 'text-purple-300' : 'text-gray-300'
                    }`}
                    onClick={scrollToTop}
                    title="Back to top"
                  >
                    On this page
                  </h4>
                  {/* Toggle button for dropdown/collapse */}
                  <button
                    onClick={(e) => { e.stopPropagation(); setTocOpen((s) => !s); }}
                    aria-expanded={tocOpen}
                    aria-label={tocOpen ? 'Collapse table of contents' : 'Expand table of contents'}
                    className="text-gray-400 hover:text-purple-300 p-1 rounded focus:outline-none"
                    title={tocOpen ? 'Collapse' : 'Expand'}
                  >
                    <svg className={`w-4 h-4 transform transition-transform duration-200 ${tocOpen ? 'rotate-180' : ''}`} viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.24 4.24a.75.75 0 01-1.06 0L5.21 8.29a.75.75 0 01.02-1.06z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>

                { /* Render nested TOC with per-heading collapse */ }
                <div className={`${tocOpen ? '' : 'hidden'}`}>
                  {headings.length === 0 ? (
                    <p className="text-gray-500">No sections</p>
                  ) : (
                    (() => {
                      const tree = buildTOCTree(headings);

                      function renderNodes(nodes) {
                        return (
                          <ul className="space-y-2 text-sm">
                            {nodes.map((node) => {
                              const hasChildren = node.children && node.children.length > 0;
                              const isCollapsed = collapsed.has(node.id);
                              const isActive = activeId === node.id;
                              const indent = node.level === 1 ? 'pl-0' : node.level === 2 ? 'pl-3' : node.level === 3 ? 'pl-6' : 'pl-9';

                              return (
                                <li key={node.id} className={indent}>
                                  <div className="flex items-center gap-2">
                                    {hasChildren ? (
                                      <button
                                        onClick={(e) => { e.stopPropagation(); toggleCollapse(node.id); }}
                                        aria-expanded={!isCollapsed}
                                        className="text-gray-400 hover:text-purple-300 p-1 rounded focus:outline-none"
                                        title={isCollapsed ? 'Expand' : 'Collapse'}
                                      >
                                        <svg className={`w-3 h-3 transform transition-transform duration-200 ${isCollapsed ? '' : 'rotate-90'}`} viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                                          <path d="M6 4l8 6-8 6V4z" />
                                        </svg>
                                      </button>
                                    ) : (
                                      <span className="w-3 h-3 inline-block" />
                                    )}

                                    <button
                                      onClick={() => scrollToHeading(node.id)}
                                      className={`text-left w-full transition-colors transform transition-transform duration-150 ease-out hover:scale-105 ${
                                        isActive ? 'text-purple-300 font-semibold' : 'text-gray-300 hover:text-purple-300'
                                      }`}
                                    >
                                      {node.text}
                                    </button>
                                  </div>

                                  {hasChildren && !isCollapsed && (
                                    <div className="mt-1">
                                      {renderNodes(node.children)}
                                    </div>
                                  )}
                                </li>
                              );
                            })}
                          </ul>
                        );
                      }

                      return renderNodes(tree);
                    })()
                  )}
                </div>
               </div>
             </nav>

            {/* Article content */}
            <article className="prose prose-lg prose-invert max-w-none flex-1">
              <header className="mb-8">
                {/* Featured Image */}
                {selectedPost.featured_image && (
                  <div className="mb-6">
                    <img 
                      src={selectedPost.featured_image} 
                      alt={selectedPost.title}
                      className="w-full h-64 md:h-80 object-cover rounded-xl"
                    />
                  </div>
                )}
                
                <p className="text-sm text-purple-400 font-semibold mb-2">{selectedPost.category}</p>
                <h1 
                  onClick={scrollToTop}
                  className="text-4xl md:text-5xl font-bold text-white leading-tight mb-4 cursor-pointer hover:text-purple-100 transition-colors"
                  title="Click to go to top"
                >
                  {selectedPost.title}
                </h1>
                <p className="text-gray-400 mb-4">{selectedPost.date}</p>
                
                {/* Tags */}
                {selectedPost.tags && selectedPost.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mb-6">
                    {selectedPost.tags.map((tag, index) => (
                      <span 
                        key={index}
                        className="text-sm bg-purple-600/20 text-purple-300 px-3 py-1 rounded-full border border-purple-500/30"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                )}
              </header>

              <div className="prose prose-lg prose-invert max-w-none">
                <ReactMarkdown
                  components={{
                    h1: ({ children, ...props }) => {
                      const text = Array.isArray(children) ? children.join('') : String(children);
                      const id = slugify(text);
                      return <h1 id={id} className="text-3xl font-bold text-white mt-8 mb-4" {...props}>{children}</h1>;
                    },
                    h2: ({ children, ...props }) => {
                      const text = Array.isArray(children) ? children.join('') : String(children);
                      const id = slugify(text);
                      return <h2 id={id} className="text-2xl font-bold text-white mt-6 mb-3" {...props}>{children}</h2>;
                    },
                    h3: ({ children, ...props }) => {
                      const text = Array.isArray(children) ? children.join('') : String(children);
                      const id = slugify(text);
                      return <h3 id={id} className="text-xl font-bold text-white mt-5 mb-2" {...props}>{children}</h3>;
                    },
                    h4: ({ children, ...props }) => {
                      const text = Array.isArray(children) ? children.join('') : String(children);
                      const id = slugify(text);
                      return <h4 id={id} className="text-lg font-bold text-white mt-4 mb-2" {...props}>{children}</h4>;
                    },
                    p: ({ children }) => <p className="text-gray-300 leading-relaxed mb-4">{children}</p>,
                    li: ({ children }) => <li className="text-gray-300 mb-1">{children}</li>,
                    blockquote: ({ children }) => (
                      <blockquote className="border-l-4 border-purple-500 pl-6 my-6 italic text-gray-300 bg-gray-800/30 py-4 rounded-r-lg">
                        {children}
                      </blockquote>
                    ),
                  }}
                >
                  {selectedPost.body}
                </ReactMarkdown>
              </div>

              {/* Back button at bottom */}
              <div className="mt-12 pt-8 border-t border-gray-800">
                <button
                  onClick={closePost}
                  className="inline-flex items-center font-semibold py-3 px-6 rounded-xl text-base bg-purple-600 hover:bg-purple-700 text-white transition-colors"
                >
                  ← Back to Blog
                </button>
              </div>
            </article>
          </div>
        </div>
      </div>
    );
  }

  // Show blog post list
  return (
    <div className="container mx-auto px-6 py-8">
      <div className="mb-6">
        <h1 className="text-2xl md:text-3xl font-bold text-white mb-2">From Our Blog</h1>
        <p className="text-gray-400 text-sm">Insights on AI, technology, and development</p>
      </div>
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {posts.length === 0 ? (
          <div className="col-span-full text-center py-8">
            <p className="text-gray-400">No posts yet.</p>
          </div>
        ) : (
          posts.map((post) => (
            <a
              key={post.id}
              href={`/blog/${post.token}`}
              onClick={(e) => {
                // Allow default for modified clicks (cmd/ctrl/middle/right open in new tab)
                if (e.metaKey || e.ctrlKey || e.shiftKey || e.altKey || e.button !== 0) return;
                e.preventDefault();
                openPost(post);
                // Update the URL for shareability / history
                try {
                  window.history.pushState(null, '', `/blog/${post.token}`);
                } catch (err) {}
              }}
              className="no-underline"
            >
              <article
                className="bg-gray-800/50 p-6 rounded-xl border border-gray-700 hover:border-purple-500/50 transition-all duration-200 cursor-pointer transform hover:-translate-y-1 h-full flex flex-col"
              >
                {/* Featured Image */}
                {post.featured_image && (
                  <div className="mb-4 -mx-6 -mt-6">
                    <img 
                      src={post.featured_image} 
                      alt={post.title}
                      className="w-full h-48 object-cover rounded-t-xl"
                    />
                  </div>
                )}
                
                <div className="flex-1">
                  <p className="text-xs text-purple-400 font-semibold mb-3">{post.category}</p>
                  <h3 className="text-lg font-bold text-white mb-2 line-clamp-2">{post.title}</h3>
                  <p className="text-xs text-gray-500 mb-3">{post.date}</p>
                  <p className="text-gray-300 text-sm leading-relaxed mb-4 line-clamp-3">{post.excerpt}</p>
                  
                  {/* Tags */}
                  {post.tags && post.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-4">
                      {post.tags.slice(0, 3).map((tag, index) => (
                        <span 
                          key={index}
                          className="text-xs bg-gray-700/50 text-gray-300 px-2 py-1 rounded-md"
                        >
                          {tag}
                        </span>
                      ))}
                      {post.tags.length > 3 && (
                        <span className="text-xs text-gray-500">+{post.tags.length - 3} more</span>
                      )}
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-between mt-auto pt-2">
                  <span className="inline-flex items-center font-medium py-2 px-4 rounded-lg text-sm bg-purple-600/90 hover:bg-purple-700 text-white transition-colors">
                    Read More
                  </span>
                  <span className="text-sm text-purple-400">→</span>
                </div>
              </article>
            </a>
          ))
        )}
      </div>
    </div>
  );
}
