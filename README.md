# Froyolabs Website

A modern, responsive website for Froyolabs AI consulting services built with React, Vite, and Tailwind CSS.

## Features

- 🚀 **Modern Tech Stack**: React 18, Vite, Tailwind CSS
- 📱 **Responsive Design**: Mobile-first approach with beautiful UI
- ⚡ **Fast Performance**: Optimized with Vite for lightning-fast development
- 🎨 **Beautiful UI**: Modern gradient designs and smooth animations
- 📈 **SEO Optimized**: Schema.org structured data and meta tags
- 🔒 **Accessible**: ARIA labels and semantic HTML

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd froyolabs-website
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and visit `http://localhost:3000`

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally

## Project Structure

```
src/
├── components/          # Reusable React components
├── styles/             # CSS and Tailwind styles
├── App.jsx            # Main application component
└── main.jsx           # Application entry point
```

## Technologies Used

- **React** - UI library
- **Vite** - Build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **Lucide React** - Beautiful icon library
- **React Helmet Async** - Document head management

## Deployment

Build the project for production:

```bash
npm run build
```

The built files will be in the `dist` directory, ready for deployment to any static hosting service.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is licensed under the MIT License.
