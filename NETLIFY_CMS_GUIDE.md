# Netlify CMS Setup Guide

This website now includes Netlify CMS for easy content management. Here's how to set it up and use it:

## Setup Instructions

### 1. Deploy to Netlify
1. Connect your repository to Netlify
2. Set the build command to `npm run build`
3. Set the publish directory to `dist`

### 2. Enable Netlify Identity
1. Go to your Netlify site dashboard
2. Navigate to **Identity** tab
3. Click **Enable Identity**
4. Go to **Settings & usage** → **Registration preferences**
5. Set to **Invite only** (recommended for security)

### 3. Enable Git Gateway
1. In the **Identity** tab, go to **Services**
2. Click **Enable Git Gateway**
3. This allows Netlify CMS to commit directly to your repository

### 4. Configure External OAuth (Optional)
For easier login, you can configure external providers:
1. Go to **Identity** → **Settings & usage** → **External providers**
2. Add providers like GitHub, Google, etc.

## Using Netlify CMS

### Accessing the Admin Interface
- Visit `your-site.netlify.app/admin`
- Or use the Admin link in the navigation (visible in development)

### Creating Content
1. Log in to the admin interface
2. Click **New Blog** to create a new blog post
3. Fill in the required fields:
   - **Title**: The post title
   - **Category**: Choose from predefined categories
   - **Date**: Publication date
   - **Featured Image**: Optional hero image
   - **Tags**: Relevant tags for the post
   - **Body**: Main content in Markdown

### Editorial Workflow
The CMS supports a 3-stage editorial workflow:
- **Draft**: Work in progress
- **In Review**: Ready for review
- **Ready**: Approved and ready to publish

### Content Features

#### Supported Fields
- Title (required)
- Category dropdown
- Publication date
- Featured image upload
- Tags (multiple)
- Excerpt (auto-generated if blank)
- Draft status toggle
- Markdown body content

#### Image Management
- Upload images to `/public/uploads/`
- Images are automatically optimized
- Use the rich media interface for easy insertion

### File Structure
- Blog posts are stored in `src/content/posts/`
- Images are stored in `public/uploads/`
- Configuration is in `public/admin/config.yaml`

## Development

### Local Development
1. Run `npm run dev` to start the development server
2. Visit `http://localhost:5173/admin` to access the CMS locally
3. You'll need to authenticate with your Netlify Identity account

### Configuration
Edit `public/admin/config.yaml` to modify:
- Content collections
- Field types
- Editorial workflow settings
- Media folder locations

## Content Format

The CMS supports both:

1. **New format** (frontmatter + markdown):
```markdown
---
title: "Post Title"
category: "Technology"
date: "2025-01-15T10:00:00.000Z"
featured_image: "/uploads/image.jpg"
tags: ["tag1", "tag2"]
draft: false
---

# Post Content
Your markdown content here...
```

2. **Legacy format** (inline metadata):
```markdown
# Post Title

**Category:** Technology
**Date:** January 15, 2025

Your content here...
```

## Security Notes

- Always use "Invite only" registration
- The admin link only shows in development or with `?admin=true` parameter
- Content is committed directly to your Git repository
- All changes are version controlled

## Troubleshooting

### Can't access admin interface
- Ensure Netlify Identity is enabled
- Check that Git Gateway is configured
- Verify you're invited to the site

### Images not uploading
- Check the media folder configuration in `config.yaml`
- Ensure the `public/uploads/` directory exists

### Posts not appearing
- Check if posts are marked as drafts
- Verify the file format is correct
- Check the browser console for errors
