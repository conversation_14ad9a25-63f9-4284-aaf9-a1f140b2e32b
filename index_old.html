import React, { useMemo, useState } from "react";
import { <PERSON><PERSON><PERSON>, Helmet<PERSON>rovider } from "react-helmet-async";
import { BrainCircuit, ShieldCheck, Bot, ArrowRight, Mail, Menu, X, Rss, Phone, ServerCog, Zap } from "lucide-react";

// ============================
// Config — tweak these values
// ============================
const SITE = {
  name: "Froyolabs",
  domain: "https://froyolabs.ai",
  email: "<EMAIL>",
  twitterImage: "https://froyolabs.ai/twitter-image.png",
  ogImage: "https://froyolabs.ai/og-image.png",
  logo: "https://froyolabs.ai/logo.png",
  phone: "******-123-4567",
};

// Reusable Button
const Button = ({ children, href, onClick, variant = "primary", className = "" }) => {
  const base =
    "inline-flex items-center justify-center font-semibold py-3 px-6 rounded-xl text-base transition-transform duration-200 hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-purple-400";
  const variants = {
    primary: "bg-purple-600 hover:bg-purple-700 text-white",
    ghost: "bg-transparent border border-gray-700 hover:border-purple-500 text-gray-200",
  };
  const cls = `${base} ${variants[variant]} ${className}`;
  if (href) {
    return (
      <a href={href} onClick={onClick} className={cls}>
        {children}
      </a>
    );
  }
  return (
    <button onClick={onClick} className={cls}>
      {children}
    </button>
  );
};

// Service Card
const ServiceCard = ({ icon, title, description }) => (
  <article className="bg-gray-800/50 p-8 rounded-xl shadow-lg hover:shadow-purple-500/20 border border-gray-700 hover:border-purple-500/50 transition-all duration-300 transform hover:-translate-y-1 text-center">
    <div className="flex justify-center mb-4" aria-hidden>
      {icon}
    </div>
    <h3 className="text-xl font-bold text-white mb-2">{title}</h3>
    <p className="text-gray-400 text-sm leading-relaxed">{description}</p>
  </article>
);

// Blog Card (stub/demo)
const BlogCard = ({ title, category, excerpt, date }) => (
  <article className="bg-gray-800/50 rounded-xl overflow-hidden border border-gray-700 hover:border-purple-500/50 transition-all duration-300 flex flex-col">
    <div className="p-6 flex-1">
      <p className="text-xs text-purple-400 font-semibold mb-2">{category}</p>
      <h3 className="text-lg font-bold text-white mb-2">{title}</h3>
      <p className="text-gray-400 text-sm">{excerpt}</p>
    </div>
    <div className="bg-gray-800 px-6 py-3 text-xs text-gray-500 flex items-center justify-between">
      <span>{date}</span>
      <a href="#" className="text-purple-400 hover:text-purple-300 font-semibold">Read More →</a>
    </div>
  </article>
);

const Header = ({ onNav, active, isOpen, setOpen }) => {
  const links = [
    { id: "home", title: "Home", type: "page" },
    { id: "services", title: "Services", type: "scroll" },
    { id: "faq", title: "FAQ", type: "scroll" },
    { id: "blog", title: "Blog", type: "page" },
    { id: "contact", title: "Contact", type: "scroll" },
  ];
  return (
    <header className="sticky top-0 z-50 bg-gray-900/80 backdrop-blur-sm shadow-md shadow-purple-500/10">
      <nav className="container mx-auto px-6 py-4 flex justify-between items-center" aria-label="Primary">
        <a
          aria-label={`${SITE.name} home`}
          href="#"
          onClick={(e) => {
            e.preventDefault();
            onNav({ id: "home", type: "page" });
          }}
          className="text-2xl font-bold text-white flex items-center"
        >
          <BrainCircuit className="text-purple-400 mr-2 h-8 w-8" aria-hidden />
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-pink-500">
            {SITE.name}
          </span>
        </a>
        <div className="hidden md:flex items-center space-x-8">
          {links.map((l) => (
            <a
              key={l.id}
              href={`#${l.id}`}
              onClick={(e) => {
                e.preventDefault();
                onNav(l);
              }}
              className={`text-sm transition-colors ${active === l.id ? "text-purple-400 font-semibold" : "text-gray-300 hover:text-purple-300"}`}
            >
              {l.title}
            </a>
          ))}
        </div>
        <Button
          href="#contact"
          onClick={(e) => {
            e.preventDefault();
            onNav({ id: "contact", type: "scroll" });
          }}
          className="hidden md:inline-flex"
        >
          Book a Call <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
        <div className="md:hidden">
          <button aria-label="Open menu" onClick={() => setOpen(!isOpen)} className="text-gray-200">
            {isOpen ? <X size={26} /> : <Menu size={26} />}
          </button>
        </div>
      </nav>
      {isOpen && (
        <div className="md:hidden bg-gray-900/95">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 flex flex-col items-center">
            {links.map((l) => (
              <a
                key={l.id}
                href={`#${l.id}`}
                onClick={(e) => {
                  e.preventDefault();
                  setOpen(false);
                  onNav(l);
                }}
                className={`block w-full text-center px-3 py-2 rounded-md text-base font-medium transition-colors ${
                  active === l.id ? "bg-purple-600 text-white" : "text-gray-300 hover:bg-gray-700 hover:text-white"
                }`}
              >
                {l.title}
              </a>
            ))}
            <Button
              href="#contact"
              onClick={(e) => {
                e.preventDefault();
                setOpen(false);
                onNav({ id: "contact", type: "scroll" });
              }}
              className="mt-2 w-full"
            >
              Book a Call
            </Button>
          </div>
        </div>
      )}
    </header>
  );
};

const Footer = ({ onNav }) => (
  <footer className="bg-gray-900 border-t border-gray-800" role="contentinfo">
    <div className="container mx-auto px-6 py-8">
      <div className="flex flex-col md:flex-row justify-between items-center">
        <div className="mb-6 md:mb-0">
          <a
            href="#"
            onClick={(e) => {
              e.preventDefault();
              onNav({ id: "home", type: "page" });
            }}
            className="text-2xl font-bold text-white flex items-center"
            aria-label={`${SITE.name} home`}
          >
            <BrainCircuit className="text-purple-400 mr-2 h-8 w-8" aria-hidden />
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-pink-500">{SITE.name}</span>
          </a>
          <p className="mt-2 text-gray-400">Secure & Private AI Solutions.</p>
        </div>
        <div className="flex space-x-6" aria-label="Social and contact links">
          <a href="#" className="text-gray-400 hover:text-purple-400"><Rss size={22} /></a>
          <a href="#" className="text-gray-400 hover:text-purple-400"><Phone size={22} /></a>
          <a href={`mailto:${SITE.email}`} className="text-gray-400 hover:text-purple-400"><Mail size={22} /></a>
        </div>
      </div>
      <div className="mt-8 border-t border-gray-800 pt-6 text-center text-gray-500">
        <p>© {new Date().getFullYear()} {SITE.name}. All rights reserved.</p>
      </div>
    </div>
  </footer>
);

// FAQ Section + Schema
const FAQ = () => {
  const faqs = [
    {
      q: "Do you only consult, or do you also build?",
      a: "We do both. From strategy and architecture to hands-on implementation, optimization, and launch.",
    },
    {
      q: "What is an AI inference pipeline?",
      a: "A production system that serves AI models with low latency and high throughput across GPUs/CPUs with autoscaling, observability, and cost controls.",
    },
    {
      q: "Can you turn my model into a SaaS?",
      a: "Yes. We design multi-tenant SaaS around your AI service: auth, billing, APIs/SDKs, dashboards, and DevOps.",
    },
    {
      q: "Which models and stacks do you support?",
      a: "OpenAI/GPT, Llama, Mistral, and domain-specific models. Stacks include Triton, KServe, Ray Serve, ONNX/TensorRT, and Kubernetes/serverless.",
    },
    {
      q: "Can you deploy on-prem for privacy?",
      a: "Absolutely. We deliver private, air‑gapped or VPC deployments with compliance-friendly architecture for regulated industries.",
    },
  ];

  const faqSchema = useMemo(
    () => ({
      "@context": "https://schema.org",
      "@type": "FAQPage",
      mainEntity: faqs.map((f) => ({
        "@type": "Question",
        name: f.q,
        acceptedAnswer: { "@type": "Answer", text: f.a },
      })),
    }),
    []
  );

  return (
    <section id="faq" className="py-20 bg-gray-900">
      <div className="container mx-auto px-6">
        <div className="text-center mb-10">
          <h2 className="text-3xl md:text-4xl font-bold text-white">Frequently Asked Questions</h2>
          <p className="text-purple-400 mt-2">Clear answers AI search engines can quote</p>
        </div>
        <div className="max-w-3xl mx-auto divide-y divide-gray-800">
          {faqs.map((f, i) => (
            <details key={i} className="group py-4">
              <summary className="cursor-pointer list-none text-left text-white font-medium flex items-center justify-between">
                <span>{f.q}</span>
                <span className="ml-4 text-gray-400 group-open:rotate-90 transition-transform">›</span>
              </summary>
              <p className="text-gray-400 mt-2 leading-relaxed">{f.a}</p>
            </details>
          ))}
        </div>
        {/* FAQ Schema for AI/SEO */}
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(faqSchema) }} />
      </div>
    </section>
  );
};

const Home = ({ onScrollToContact }) => {
  const services = [
    {
      icon: <Zap className="h-12 w-12 text-purple-400" />,
      title: "AI Inference Pipeline Optimization",
      description:
        "End-to-end consulting and implementation for AI pipelines that deliver low latency, high scalability, and cost efficiency.",
    },
    {
      icon: <ShieldCheck className="h-12 w-12 text-purple-400" />,
      title: "Private AI for Sensitive Industries",
      description:
        "Custom AI on your private infrastructure for legal, healthcare, and finance with data privacy and compliance.",
    },
    {
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="48"
          height="48"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="text-purple-400"
          aria-hidden
        >
          <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
        </svg>
      ),
      title: "Cross-Platform Apps with Rust & Tauri",
      description:
        "High-performance, secure, lightweight desktop applications for Windows, macOS, and Linux from a single codebase.",
    },
    {
      icon: <Bot className="h-12 w-12 text-purple-400" />,
      title: "Intelligent Automation Agents",
      description:
        "Streamline workflows with AI agents, MCP servers, and integrations with n8n, Zapier, and Make.",
    },
    {
      icon: <ServerCog className="h-12 w-12 text-purple-400" />,
      title: "SaaS for Your AI Service",
      description:
        "We design, build, and launch SaaS around your AI — multi-tenant architecture, auth, billing, APIs/SDKs, and UX.",
    },
  ];

  // ItemList schema for services (helps AI associate offerings)
  const servicesSchema = useMemo(
    () => ({
      "@context": "https://schema.org",
      "@type": "ItemList",
      itemListElement: services.map((s, idx) => ({
        "@type": "Service",
        position: idx + 1,
        name: s.title,
        description: s.description,
        serviceType: s.title,
        provider: { "@type": "Organization", name: SITE.name },
      })),
    }),
    []
  );

  return (
    <>
      {/* Hero */}
      <section id="home" className="relative py-20 md:py-28 text-center text-white overflow-hidden">
        <div className="absolute inset-0 bg-grid-purple-500/10 [mask-image:linear-gradient(to_bottom,white_5%,transparent_100%)]" aria-hidden></div>
        <div className="absolute inset-0 bg-gradient-to-b from-gray-900 via-gray-900/80 to-gray-900" aria-hidden></div>
        <div className="container mx-auto px-6 relative">
          <h1 className="text-4xl md:text-6xl font-extrabold leading-tight mb-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-pink-500">
            AI Consulting & Implementation — ChatGPT, LLMs, and End‑to‑End AI Solutions
          </h1>
          <p className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            We architect privacy‑first AI and build blazingly fast apps. From inference pipelines to SaaS for your AI service, we deliver low latency, high scalability, and cost efficiency.
          </p>
          <Button onClick={onScrollToContact}>
            Book a Discovery Call <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </section>

      {/* Services */}
      <section id="services" className="py-18 md:py-20 bg-gray-900">
        <div className="container mx-auto px-6">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold text-white">Our Expertise</h2>
            <p className="text-purple-400 mt-2">Inference pipelines • Private AI • SaaS for AI</p>
          </div>
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {services.map((s, i) => (
              <ServiceCard key={i} {...s} />
            ))}
          </div>
        </div>
        {/* Services Schema */}
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(servicesSchema) }} />
      </section>
    </>
  );
};

const Blog = () => {
  const posts = [
    {
      title: "Why Private AI is the Future for Regulated Industries",
      category: "AI & Privacy",
      excerpt: "Why on‑prem and VPC LLMs matter for legal, healthcare, and finance.",
      date: "August 1, 2025",
    },
    {
      title: "Rust + Tauri: The Ultimate Stack for Modern Desktop Apps?",
      category: "Development",
      excerpt: "Rust performance meets Tauri’s lightweight shell for secure cross‑platform apps.",
      date: "July 22, 2025",
    },
    {
      title: "Automating Your Business with n8n and AI Agents",
      category: "Automation",
      excerpt: "Connect systems and automate workflows with n8n + custom AI agents.",
      date: "July 5, 2025",
    },
  ];
  return (
    <section className="py-20 bg-gray-900">
      <div className="container mx-auto px-6">
        <div className="text-center mb-10">
          <h2 className="text-3xl md:text-4xl font-bold text-white">From Our Blog</h2>
          <p className="text-purple-400 mt-2">Insights on AI, Development, and Automation</p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {posts.map((p, i) => (
            <BlogCard key={i} {...p} />
          ))}
        </div>
      </div>
    </section>
  );
};

const Contact = () => (
  <section id="contact" className="py-20 bg-gray-900">
    <div className="container mx-auto px-6">
      <div className="text-center mb-10">
        <h2 className="text-3xl md:text-4xl font-bold text-white">Contact Us</h2>
        <p className="text-purple-400 mt-2">Let's build something amazing together</p>
      </div>
      <div className="max-w-3xl mx-auto bg-gray-800/50 p-8 rounded-xl border border-gray-700 text-center">
        <p className="text-gray-300 text-lg mb-6">
          Have a project in mind or want to learn more? Reach out to us.
        </p>
        <Button href={`mailto:${SITE.email}`}>
          Send us an Email <Mail className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  </section>
);

// ============================
// Main App with AI/SEO schemas
// ============================
const Shell = () => {
  const [activePage, setActivePage] = useState("home");
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const onNav = (link) => {
    setIsMenuOpen(false);
    if (link.type === "page") {
      setActivePage(link.id);
      window.scrollTo({ top: 0, behavior: "smooth" });
    } else if (link.type === "scroll") {
      if (activePage !== "home") setActivePage("home");
      setTimeout(() => {
        document.getElementById(link.id)?.scrollIntoView({ behavior: "smooth" });
      }, 100);
    }
  };

  const onScrollToContact = () => {
    setActivePage("home");
    setTimeout(() => document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" }), 100);
  };

  // ======= Schema.org blocks =======
  const organizationSchema = useMemo(
    () => ({
      "@context": "https://schema.org",
      "@type": "Organization",
      name: SITE.name,
      url: SITE.domain,
      logo: SITE.logo,
      contactPoint: {
        "@type": "ContactPoint",
        email: SITE.email,
        contactType: "sales",
        availableLanguage: ["English"],
      },
      sameAs: [
        // Add your real social links
      ],
    }),
    []
  );

  const websiteSchema = useMemo(
    () => ({
      "@context": "https://schema.org",
      "@type": "WebSite",
      name: SITE.name,
      url: SITE.domain,
      potentialAction: {
        "@type": "SearchAction",
        target: `${SITE.domain}/search?q={query}`,
        "query-input": "required name=query",
      },
    }),
    []
  );

  const breadcrumbsSchema = useMemo(
    () => ({
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      itemListElement: [
        { "@type": "ListItem", position: 1, name: "Home", item: `${SITE.domain}/` },
        { "@type": "ListItem", position: 2, name: activePage === "blog" ? "Blog" : "Services", item: `${SITE.domain}/${activePage}` },
      ],
    }),
    [activePage]
  );

  return (
    <div className="bg-gray-900 text-gray-100 font-sans antialiased min-h-screen">
      <Header onNav={onNav} active={activePage} isOpen={isMenuOpen} setOpen={setIsMenuOpen} />

      {/* Routes (simple) */}
      {activePage === "blog" ? (
        <Blog />
      ) : (
        <>
          <Home onScrollToContact={onScrollToContact} />
          <FAQ />
          <Contact />
        </>
      )}

      <Footer onNav={onNav} />

      {/* Global Schemas for AI/SEO */}
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationSchema) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(websiteSchema) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbsSchema) }} />
    </div>
  );
};

export default function App() {
  // Head tags for AI/SEO + social summarizers
  return (
    <HelmetProvider>
      <Helmet>
        <html lang="en" />
        <title>AI Consultancy | Inference Pipelines & SaaS for Your AI</title>
        <meta
          name="description"
          content="End-to-end AI consulting and implementation: inference pipelines, ChatGPT/LLM integration, and SaaS around your AI service. Low latency, scalable, and cost-efficient."
        />
        <meta
          name="keywords"
          content="AI consultancy, AI inference pipelines, SaaS for AI, ChatGPT integration, LLM deployment, MLOps, Kubernetes, Triton, KServe, TensorRT, ONNX"
        />
        <meta name="author" content={SITE.name} />

        {/* Open Graph */}
        <meta property="og:title" content="AI Consultancy | Inference Pipelines & SaaS for Your AI" />
        <meta
          property="og:description"
          content="We design, build, and scale AI services — inference pipelines, private AI, and SaaS for your AI."
        />
        <meta property="og:type" content="website" />
        <meta property="og:url" content={SITE.domain} />
        <meta property="og:image" content={SITE.ogImage} />

        {/* Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="AI Consultancy | Inference Pipelines & SaaS for Your AI" />
        <meta name="twitter:description" content="End‑to‑end AI consulting & implementation for scalable, low‑latency AI." />
        <meta name="twitter:image" content={SITE.twitterImage} />

        {/* Prefer short, canonical URL */}
        <link rel="canonical" href={SITE.domain} />

        {/* Favicon (optional) */}
        {/* <link rel="icon" href="/favicon.ico" /> */}
      </Helmet>
      <Shell />
    </HelmetProvider>
  );
}
