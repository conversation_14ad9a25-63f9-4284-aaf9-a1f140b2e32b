# Password Recovery Troubleshooting Guide

## Current Issue: No Recovery Email Being Sent

Based on the console logs showing `[token-parser] Parsing hash: <empty string>`, the issue is that **no recovery email is being sent at all**, rather than the token being invalid.

## Step-by-Step Troubleshooting

### 1. **Immediate Testing Options**

You now have three ways to test the password recovery:

#### Option A: Use the Main Website (Recommended)
1. Go to `https://froyolabs.netlify.app`
2. Scroll down to the "Contact Us" section
3. Use the "🔐 Authentication Tester" component on the right
4. Enter an email address and click "Send Reset Email"
5. Check the console for detailed error messages

#### Option B: Use the CMS Admin Page
1. Go to `https://froyolabs.netlify.app/admin/`
2. You'll see a development mode banner with password reset testing
3. Enter an email and click "Send Reset Email"
4. Monitor the console for detailed logs

#### Option C: Use the Debug Tool
1. Go to `https://froyolabs.netlify.app/debug-recovery.html`
2. Use the "Test Password Reset" section
3. This provides the most detailed debugging information

### 2. **Check Netlify Identity Configuration**

The issue might be that Netlify Identity isn't properly configured:

1. **Go to Netlify Dashboard**:
   - Visit [https://app.netlify.com](https://app.netlify.com)
   - Find your "froyolabs" site
   - Go to **Site settings > Identity**

2. **Verify Identity is Enabled**:
   - Make sure "Enable Identity" is turned ON
   - Check if "Enable registration" is configured as needed

3. **Check Email Settings**:
   - Look for "Email" section in Identity settings
   - Verify email templates are properly configured
   - Make sure "Recovery email" template exists and is enabled

4. **Check Users**:
   - Go to the "Users" tab in Identity
   - Verify that users exist in the system
   - If no users exist, that's likely the problem

### 3. **Common Issues and Solutions**

#### Issue: "User Not Found" During Reset
**Cause**: The email address doesn't exist in Netlify Identity
**Solution**: 
- Create an account first using the CMS admin or the AuthTester
- Verify the correct email address is being used

#### Issue: No Email Being Sent (Current Issue)
**Possible Causes**:
1. **Identity not enabled**: Check Netlify dashboard
2. **Email service not configured**: Check email settings in Netlify
3. **API endpoint issues**: Check if the site's Identity API is responding
4. **Rate limiting**: Too many requests in a short period

**Solutions**:
1. Enable Identity in Netlify dashboard
2. Configure email templates and SMTP settings
3. Test the API endpoint using the debug tools
4. Wait 10-15 minutes between reset attempts

#### Issue: Recovery Email Goes to Spam
**Solution**: Check spam/junk folder in your email

#### Issue: JavaScript Errors
**Solution**: Check browser console for errors and report them

### 4. **Testing Workflow**

1. **First, test if a user account exists**:
   ```
   Use AuthTester > Enter email > Send Reset Email
   ```
   - If you get "user not found" → Create account first
   - If you get "email sent" → Check your inbox
   - If you get other errors → Check Netlify configuration

2. **Create a test account if needed**:
   ```
   Go to /admin/ > Click "Login with Netlify Identity" > Sign up
   ```

3. **Test the full recovery flow**:
   ```
   Request reset → Check email → Click recovery link → Reset password
   ```

### 5. **Debugging Console Messages**

When you test, look for these console messages:

#### Success Indicators:
```
[netlify-identity] Production widget initialized successfully
[auth-tester] Password reset email sent to: <EMAIL>
[netlify-identity] Widget initialized, current user: <EMAIL>
```

#### Error Indicators:
```
[netlify-identity] Production error details: User not found
[auth-tester] Password reset error: User not found
[netlify-identity] API probe failed with status: 404
```

### 6. **Next Steps Based on Results**

#### If AuthTester shows "Email sent successfully":
- Check your email inbox (including spam)
- The recovery flow should work when you click the email link

#### If AuthTester shows "User not found":
- Create a new account first via the CMS admin
- Verify you're using the correct email address

#### If AuthTester shows API or network errors:
- Check Netlify Identity configuration
- Verify the site is properly deployed
- Check if there are any service outages

#### If AuthTester doesn't load:
- Check browser console for JavaScript errors
- Verify the site is loading correctly
- Try refreshing the page

### 7. **Contact Information**

If none of these steps resolve the issue, please provide:
1. The exact error message from the console
2. The email address you're testing with
3. Whether you can see users in the Netlify Identity dashboard
4. Screenshots of the Netlify Identity settings page

This will help identify if it's a configuration issue, a user account issue, or something else.

---

## Updated Files Summary

The recovery workflow has been enhanced with:
- ✅ Comprehensive error handling and user feedback
- ✅ AuthTester component for easy testing
- ✅ Debug tools and detailed logging
- ✅ Improved token parsing and validation
- ✅ Better error messages for different scenarios

You should now be able to identify exactly what's preventing the recovery emails from being sent.
