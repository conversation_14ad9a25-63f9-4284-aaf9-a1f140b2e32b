<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recovery Token Debug - Froyolabs</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .debug-info { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { background: #fee; border: 1px solid #fcc; }
        .success { background: #efe; border: 1px solid #cfc; }
        .warning { background: #ffc; border: 1px solid #ffb; }
        pre { white-space: pre-wrap; word-break: break-word; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a8b; }
        .copy-btn { font-size: 12px; padding: 5px 10px; }
    </style>
</head>
<body>
    <h1>Recovery Token Debug Tool</h1>
    <p>This page helps debug issues with Netlify Identity recovery tokens.</p>
    
    <div id="token-info" class="debug-info">
        <h3>Token Information</h3>
        <div id="token-details">No token detected in URL</div>
    </div>
    
    <div id="api-info" class="debug-info">
        <h3>API Configuration</h3>
        <div id="api-details">Loading...</div>
    </div>
    
    <div id="identity-status" class="debug-info">
        <h3>Identity Widget Status</h3>
        <div id="identity-details">Loading...</div>
    </div>
    
    <div id="test-results" class="debug-info">
        <h3>Test Results</h3>
        <div id="test-details">
            <button onclick="testIdentityAPI()">Test Identity API</button>
            <button onclick="testTokenProcessing()">Test Token Processing</button>
            <button onclick="listUsers()">List Users (Debug)</button>
        </div>
        <div id="test-output"></div>
    </div>
    
    <div id="recovery-instructions" class="debug-info warning">
        <h3>🔍 Your Specific Recovery Token Analysis</h3>
        
        <div style="margin-bottom: 1rem; padding: 15px; background: #fef3c7; border: 1px solid #f59e0b; border-radius: 5px;">
            <h4 style="color: #92400e; margin-top: 0;">🎯 Analyzing Your Token</h4>
            <p><strong>Your Recovery URL:</strong></p>
            <code style="background: #fff; padding: 5px; border-radius: 3px; display: block; margin: 5px 0; word-break: break-all;">
                https://froyolabs.netlify.app/#recovery_token=bCSVJDsEeB8t_GjeKUGuZQ
            </code>
            <p><strong>Token:</strong> <code>bCSVJDsEeB8t_GjeKUGuZQ</code> (22 chars - valid Netlify format)</p>
            <p><strong>Site:</strong> froyolabs.netlify.app ✅</p>
            <p><strong>Status:</strong> <span style="color: #dc2626;">❌ "User not found" error</span></p>
            
            <div style="margin-top: 10px;">
                <button onclick="testSpecificToken()" style="margin: 2px; padding: 8px 12px; background: #3b82f6; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    🧪 Test This Token
                </button>
                <button onclick="analyzeToken('bCSVJDsEeB8t_GjeKUGuZQ')" style="margin: 2px; padding: 8px 12px; background: #10b981; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    🔍 Deep Analysis
                </button>
            </div>
        </div>
        
        <div style="margin-bottom: 1rem; padding: 15px; background: #fee; border: 1px solid #fcc; border-radius: 5px;">
            <h4 style="color: #c00; margin-top: 0;">⚠️ Most Likely Cause</h4>
            <p><strong>The recovery token is valid, but the user account no longer exists.</strong></p>
            <p>This happens when:</p>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li><strong>Account deleted:</strong> User was removed from Netlify Identity after email was sent</li>
                <li><strong>Identity reset:</strong> The Identity database was cleared/reset</li>
                <li><strong>Wrong deployment:</strong> Email was sent from a different Netlify site</li>
                <li><strong>Test environment:</strong> Email was sent from staging but you're on production</li>
            </ul>
            
            <div style="margin-top: 15px; padding: 10px; background: #e0f2fe; border-radius: 4px;">
                <strong>🔧 Quick Fix:</strong> 
                <a href="/admin/" target="_blank" style="color: #1565c0;">Go to CMS Admin</a> → 
                Create new account with same email → 
                Request fresh password reset
            </div>
        </div>

        <h3>Testing Instructions</h3>
        
        <div style="margin-bottom: 1rem; padding: 15px; background: #fee; border: 1px solid #fcc; border-radius: 5px;">
            <h4 style="color: #c00; margin-top: 0;">⚠️ Current Issue: User Not Found</h4>
            <p>You're getting "user not found" even with a correct email. This usually means:</p>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li><strong>Wrong site:</strong> Recovery email was sent from a different Netlify deployment</li>
                <li><strong>Account deleted:</strong> User was removed after email was sent</li>
                <li><strong>Identity reset:</strong> The Identity database was reset</li>
                <li><strong>Configuration mismatch:</strong> Local vs production Identity settings</li>
            </ul>
        </div>
        
        <div style="margin-bottom: 1rem;">
            <h4>🔍 Option 1: Analyze Your Recovery Email</h4>
            <ol>
                <li>Find the original password reset email in your inbox</li>
                <li>Copy the ENTIRE recovery URL from the email: 
                    <input type="text" id="recovery-url" style="width: 100%; margin: 5px 0; padding: 5px;" placeholder="https://site-name.netlify.app/#recovery_token=...">
                </li>
                <li><button onclick="analyzeRecoveryUrl()">Analyze Recovery URL</button></li>
                <li>This will show you which site sent the email and verify the token format</li>
            </ol>
            <div id="recovery-analysis" style="margin: 10px 0; padding: 10px; border-radius: 5px; display: none;"></div>
        </div>
        
        <div style="margin-bottom: 1rem;">
            <h4>📧 Option 2: Test Password Reset</h4>
            <ol>
                <li>Enter the SAME email from your recovery link: <input type="email" id="test-email" style="width: 300px; margin: 5px; padding: 5px;" placeholder="<EMAIL>"></li>
                <li><button onclick="sendPasswordReset()">Send Password Reset Email</button></li>
                <li>If this succeeds, your account exists but the old token might be invalid</li>
                <li>If this fails with "user not found", then the account doesn't exist in this site's Identity</li>
            </ol>
            <div id="reset-test-result" style="margin: 10px 0; padding: 10px; border-radius: 5px; display: none;"></div>
        </div>
        
        <div style="margin-bottom: 1rem;">
            <h4>🏢 Option 3: Check Multiple Sites</h4>
            <p>If you have multiple Netlify sites, the recovery email might be from a different one:</p>
            <ol>
                <li>Check your email for the sender domain in the recovery email</li>
                <li>Look for other Netlify sites you might have: <input type="text" id="other-site" style="width: 300px; margin: 5px; padding: 5px;" placeholder="other-site.netlify.app"></li>
                <li><button onclick="checkOtherSite()">Test Other Site</button></li>
            </ol>
            <div id="other-site-result" style="margin: 10px 0; padding: 10px; border-radius: 5px; display: none;"></div>
        </div>
        
        <div>
            <h4>👤 Option 4: Create New Account</h4>
            <ol>
                <li>If the account is truly missing, create a new one:</li>
                <li><button onclick="openSignup()">Create New Account</button></li>
                <li>Use the SAME email address as your recovery link</li>
                <li>After creating, you can log in normally</li>
            </ol>
        </div>
    </div>

    <script src="https://identity.netlify.com/v1/netlify-identity-widget.js"></script>
    <script>
        // Debug information collection
        const debugInfo = {
            url: window.location.href,
            hostname: window.location.hostname,
            hash: window.location.hash,
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString()
        };

        // Token parsing function
        function parseToken() {
            const hash = window.location.hash.replace(/^#/, '');
            if (!hash) return null;
            
            try {
                const params = new URLSearchParams(hash);
                const token = params.get('recovery_token') || params.get('confirmation_token') || params.get('token');
                if (token) {
                    return {
                        type: params.get('recovery_token') ? 'recovery' : params.get('confirmation_token') ? 'confirmation' : 'generic',
                        token: token,
                        fullHash: hash
                    };
                }
            } catch (e) {
                console.error('Token parsing error:', e);
            }
            return null;
        }

        function parseRecoveryUrl() {
            const url = document.getElementById('recovery-url').value;
            if (!url) return;
            
            try {
                const urlObj = new URL(url);
                const hash = urlObj.hash.replace(/^#/, '');
                const params = new URLSearchParams(hash);
                const token = params.get('recovery_token');
                
                document.getElementById('token-details').innerHTML = `
                    <strong>Parsed URL:</strong> ${url}<br>
                    <strong>Hash:</strong> ${hash}<br>
                    <strong>Token:</strong> ${token ? token.substring(0, 50) + '...' : 'Not found'}<br>
                    <button class="copy-btn" onclick="copyToClipboard('${token}')">Copy Token</button>
                `;
                
                // Navigate to this token for testing
                if (token) {
                    window.location.hash = `recovery_token=${token}`;
                    setTimeout(initializeDebug, 100);
                }
            } catch (e) {
                alert('Invalid URL format');
            }
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                alert('Token copied to clipboard');
            });
        }

        function initializeDebug() {
            const tokenInfo = parseToken();
            
            // Check if we have a token from the URL hash
            if (!tokenInfo) {
                // Check if there's a specific token provided in the URL
                const urlParams = new URLSearchParams(window.location.search);
                const providedToken = urlParams.get('token');
                if (providedToken) {
                    window.location.hash = `recovery_token=${providedToken}`;
                    setTimeout(initializeDebug, 100);
                    return;
                }
            }
            
            // Display token information
            if (tokenInfo) {
                document.getElementById('token-details').innerHTML = `
                    <div class="success">
                        <strong>Type:</strong> ${tokenInfo.type}<br>
                        <strong>Token (first 50 chars):</strong> ${tokenInfo.token.substring(0, 50)}...<br>
                        <strong>Full Token:</strong> <span style="font-family: monospace; font-size: 12px; word-break: break-all;">${tokenInfo.token}</span><br>
                        <strong>Full Hash:</strong> ${tokenInfo.fullHash}<br>
                        <button class="copy-btn" onclick="copyToClipboard('${tokenInfo.token}')">Copy Full Token</button>
                        <button class="copy-btn" onclick="analyzeToken('${tokenInfo.token}')">Analyze Token</button>
                    </div>
                `;
                
                // Auto-analyze the token if it looks like the one from the user
                if (tokenInfo.token === 'bCSVJDsEeB8t_GjeKUGuZQ') {
                    setTimeout(() => analyzeToken(tokenInfo.token), 500);
                }
            } else {
                document.getElementById('token-details').innerHTML = `
                    <div class="warning">
                        No recovery token found in URL hash<br>
                        <strong>Tip:</strong> Add your recovery token to the URL like this:<br>
                        <code>debug-recovery.html#recovery_token=YOUR_TOKEN</code><br>
                        <br>
                        <strong>Quick Test:</strong> <button onclick="testSpecificToken()">Test Known Token</button>
                    </div>
                `;
            }

            // Check Identity widget
            if (window.netlifyIdentity) {
                document.getElementById('identity-details').innerHTML = '<div class="success">Netlify Identity widget loaded successfully</div>';
                
                // Initialize Identity
                const PROD_IDENTITY_API = 'https://froyolabs.netlify.app/.netlify/identity';
                const isLocal = (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');
                const initOptions = isLocal ? { APIUrl: PROD_IDENTITY_API } : {};
                
                document.getElementById('api-details').innerHTML = `
                    <strong>Environment:</strong> ${isLocal ? 'Local Development' : 'Production'}<br>
                    <strong>API URL:</strong> ${initOptions.APIUrl || 'Default (same domain)'}<br>
                    <strong>Current URL:</strong> ${window.location.href}<br>
                    <strong>Site Origin:</strong> ${window.location.origin}
                `;

                try {
                    netlifyIdentity.init(initOptions);
                    
                    netlifyIdentity.on('error', (err) => {
                        document.getElementById('test-output').innerHTML += `
                            <div class="error">
                                <strong>Identity Error:</strong><br>
                                <strong>Message:</strong> ${err.message || 'Unknown error'}<br>
                                <strong>Type:</strong> ${err.name || 'Unknown'}<br>
                                <strong>Details:</strong><br>
                                <pre>${JSON.stringify(err, null, 2)}</pre>
                                <br>
                                <strong>Analysis:</strong><br>
                                ${analyzeError(err)}
                            </div>
                        `;
                    });

                    netlifyIdentity.on('login', (user) => {
                        document.getElementById('test-output').innerHTML += `
                            <div class="success">
                                <strong>Login Success:</strong> ${user.email}<br>
                                <strong>User ID:</strong> ${user.id}<br>
                                <strong>Created:</strong> ${user.created_at}
                            </div>
                        `;
                    });

                } catch (e) {
                    document.getElementById('identity-details').innerHTML += `<div class="error">Init failed: ${e.message}</div>`;
                }
                
            } else {
                document.getElementById('identity-details').innerHTML = '<div class="error">Netlify Identity widget not loaded</div>';
            }
        }

        function testIdentityAPI() {
            const PROD_IDENTITY_API = 'https://froyolabs.netlify.app/.netlify/identity';
            
            document.getElementById('test-output').innerHTML = '<div>Testing Identity API...</div>';
            
            fetch(PROD_IDENTITY_API, { method: 'GET', mode: 'cors' })
                .then(response => {
                    document.getElementById('test-output').innerHTML = `
                        <div class="${response.ok ? 'success' : 'error'}">
                            <strong>API Test Result:</strong><br>
                            Status: ${response.status} ${response.statusText}<br>
                            URL: ${PROD_IDENTITY_API}
                        </div>
                    `;
                    return response.text();
                })
                .then(text => {
                    document.getElementById('test-output').innerHTML += `
                        <div class="debug-info">
                            <strong>Response Body:</strong><br>
                            <pre>${text.substring(0, 500)}${text.length > 500 ? '...' : ''}</pre>
                        </div>
                    `;
                })
                .catch(err => {
                    document.getElementById('test-output').innerHTML = `
                        <div class="error">
                            <strong>API Test Failed:</strong><br>
                            <pre>${err.message}</pre>
                        </div>
                    `;
                });
        }

        function testTokenProcessing() {
            const tokenInfo = parseToken();
            if (!tokenInfo) {
                alert('No token to test. Add a recovery token to the URL hash first.');
                return;
            }

            document.getElementById('test-output').innerHTML = '<div>Testing token processing...</div>';

            if (window.netlifyIdentity) {
                try {
                    netlifyIdentity.open();
                    document.getElementById('test-output').innerHTML += '<div class="success">Identity widget opened for token processing</div>';
                } catch (e) {
                    document.getElementById('test-output').innerHTML += `<div class="error">Failed to open widget: ${e.message}</div>`;
                }
            } else {
                document.getElementById('test-output').innerHTML += '<div class="error">Identity widget not available</div>';
            }
        }

        function listUsers() {
            document.getElementById('test-output').innerHTML += '<div class="warning">User listing is only available through Netlify admin dashboard for security reasons.</div>';
        }

        function sendPasswordReset() {
            const email = document.getElementById('test-email').value;
            const resultEl = document.getElementById('reset-test-result');
            
            if (!email) {
                showResult(resultEl, 'Please enter an email address', 'error');
                return;
            }
            
            if (!window.netlifyIdentity) {
                showResult(resultEl, 'Netlify Identity widget not loaded', 'error');
                return;
            }
            
            showResult(resultEl, 'Sending password reset email...', 'info');
            
            try {
                window.netlifyIdentity.gotrue.requestPasswordRecovery(email)
                    .then(() => {
                        showResult(resultEl, `✅ Password reset email sent successfully to ${email}! Check your inbox for the recovery link.`, 'success');
                        console.log('[debug] Password reset email sent to:', email);
                    })
                    .catch((error) => {
                        console.error('[debug] Password reset error:', error);
                        let errorMsg = 'Failed to send reset email';
                        
                        if (error.message) {
                            if (error.message.toLowerCase().includes('user not found')) {
                                errorMsg = `❌ No user found with email "${email}". The account may not exist or was deleted.`;
                            } else if (error.message.toLowerCase().includes('rate limit')) {
                                errorMsg = '⏱️ Rate limit exceeded. Please wait a few minutes before trying again.';
                            } else {
                                errorMsg = `❌ Error: ${error.message}`;
                            }
                        }
                        
                        showResult(resultEl, errorMsg, 'error');
                    });
            } catch (e) {
                showResult(resultEl, `❌ Exception: ${e.message}`, 'error');
            }
        }

        function checkUserExists() {
            const email = document.getElementById('check-email').value;
            const resultEl = document.getElementById('user-check-result');
            
            if (!email) {
                showResult(resultEl, 'Please enter an email address', 'error');
                return;
            }
            
            showResult(resultEl, 'Checking if user exists...', 'info');
            
            // We'll use the password reset API to check if user exists
            // If user doesn't exist, it will return a "user not found" error
            try {
                window.netlifyIdentity.gotrue.requestPasswordRecovery(email)
                    .then(() => {
                        showResult(resultEl, `✅ User exists! Reset email would be sent to ${email}`, 'success');
                    })
                    .catch((error) => {
                        if (error.message && error.message.toLowerCase().includes('user not found')) {
                            showResult(resultEl, `❌ User does not exist with email "${email}"`, 'error');
                        } else {
                            showResult(resultEl, `✅ User likely exists (got different error: ${error.message})`, 'warning');
                        }
                    });
            } catch (e) {
                showResult(resultEl, `❌ Error checking user: ${e.message}`, 'error');
            }
        }

        function testEmailExists() {
            const email = document.getElementById('test-email-specific').value;
            const resultEl = document.getElementById('test-email-result');
            
            if (!email) {
                showResult(resultEl, 'Please enter an email address', 'error');
                return;
            }
            
            showResult(resultEl, 'Testing if any users exist in Identity...', 'info');
            
            try {
                window.netlifyIdentity.gotrue.requestPasswordRecovery(email)
                    .then(() => {
                        showResult(resultEl, `✅ Users exist in Identity! (Reset would be sent to ${email})`, 'success');
                    })
                    .catch((error) => {
                        if (error.message && error.message.toLowerCase().includes('user not found')) {
                            showResult(resultEl, `❌ Confirmed: NO users exist in this site's Identity database. This explains your "user not found" error.`, 'error');
                        } else {
                            showResult(resultEl, `✅ Identity is working (different error: ${error.message})`, 'warning');
                        }
                    });
            } catch (e) {
                showResult(resultEl, `❌ Error testing: ${e.message}`, 'error');
            }
        }

        function checkSiteConfig() {
            const resultEl = document.getElementById('config-result');
            
            showResult(resultEl, 'Checking site configuration...', 'info');
            
            const config = {
                currentUrl: window.location.href,
                origin: window.location.origin,
                expectedSite: 'froyolabs.netlify.app',
                identityLoaded: !!window.netlifyIdentity,
                gotrueAvailable: !!(window.netlifyIdentity && window.netlifyIdentity.gotrue)
            };
            
            let report = `
                <strong>Site Configuration Report:</strong><br>
                • Current URL: ${config.currentUrl}<br>
                • Site Origin: ${config.origin}<br>
                • Expected Site: ${config.expectedSite}<br>
                • Identity Loaded: ${config.identityLoaded ? '✅' : '❌'}<br>
                • GoTrue API: ${config.gotrueAvailable ? '✅' : '❌'}<br>
            `;
            
            if (config.origin.includes('froyolabs.netlify.app')) {
                report += '<br>✅ You are on the correct site (froyolabs.netlify.app)';
            } else {
                report += '<br>⚠️ You might be on a different deployment or local development';
            }
            
            showResult(resultEl, report, 'info');
        }

        function analyzeRecoveryUrl() {
            const url = document.getElementById('recovery-url').value;
            const resultEl = document.getElementById('recovery-analysis');
            
            if (!url) {
                showResult(resultEl, 'Please enter the recovery URL from your email', 'error');
                return;
            }
            
            try {
                const urlObj = new URL(url);
                const site = urlObj.hostname;
                const hash = urlObj.hash.replace(/^#/, '');
                const params = new URLSearchParams(hash);
                const token = params.get('recovery_token');
                
                let analysis = `
                    <strong>Recovery URL Analysis:</strong><br>
                    • Site: ${site}<br>
                    • Current Site: ${window.location.hostname}<br>
                    • Token: ${token ? token.substring(0, 20) + '...' : 'Not found'}<br>
                    • Match: ${site === window.location.hostname ? '✅ Same site' : '❌ Different site!'}<br>
                `;
                
                if (site !== window.location.hostname) {
                    analysis += `<br><strong>🚨 ISSUE FOUND:</strong> The recovery email was sent from <code>${site}</code> but you're on <code>${window.location.hostname}</code>. This explains the "user not found" error!<br><br>
                    <strong>Solution:</strong> Go to <a href="https://${site}" target="_blank">https://${site}</a> and try the recovery link there.`;
                }
                
                showResult(resultEl, analysis, site === window.location.hostname ? 'success' : 'error');
                
                if (token && site === window.location.hostname) {
                    // Auto-navigate to test the token
                    setTimeout(() => {
                        window.location.hash = `recovery_token=${token}`;
                        setTimeout(initializeDebug, 100);
                    }, 2000);
                }
                
            } catch (e) {
                showResult(resultEl, `❌ Invalid URL format: ${e.message}`, 'error');
            }
        }

        function analyzeToken(token) {
            document.getElementById('test-output').innerHTML = `
                <div class="debug-info">
                    <strong>🔍 Token Analysis:</strong><br>
                    <strong>Token:</strong> ${token}<br>
                    <strong>Length:</strong> ${token.length} characters<br>
                    <strong>Format:</strong> ${token.includes('_') || token.includes('-') ? 'URL-safe Base64' : 'Standard format'}<br>
                    <br>
                    <strong>🧪 Testing Token:</strong><br>
                    <div id="token-test-results">Preparing to test...</div>
                </div>
            `;
            
            setTimeout(() => {
                document.getElementById('token-test-results').innerHTML = 'Attempting to process token...';
                if (window.netlifyIdentity) {
                    try {
                        window.netlifyIdentity.open();
                    } catch (e) {
                        document.getElementById('token-test-results').innerHTML = `❌ Failed: ${e.message}`;
                    }
                }
            }, 1000);
        }

        function analyzeError(error) {
            const message = error.message || '';
            if (message.toLowerCase().includes('user not found')) {
                return `🔍 The token is valid but the user account no longer exists. Check Netlify Identity users.`;
            }
            return `🔍 Unexpected error: ${message}`;
        }

        // Initialize on page load
        window.addEventListener('load', initializeDebug);
        window.addEventListener('hashchange', initializeDebug);
    </script>
</body>
</html>
