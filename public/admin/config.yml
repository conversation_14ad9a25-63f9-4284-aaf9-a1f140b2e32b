backend:
  name: git-gateway
  branch: master   # Updated to main branch

media_folder: "public/uploads"   # where images go
public_folder: "/uploads"

# Enable editorial workflow for draft/review/ready states
publish_mode: editorial_workflow

# Site URL for preview
site_url: https://froyolabs.netlify.app
display_url: https://froyolabs.netlify.app

collections:
  - name: "blog"
    label: "Blog Posts"
    folder: "src/content/posts"
    create: true
    slug: "{{year}}-{{month}}-{{day}}-{{slug}}"
    preview_path: "/blog/{{slug}}"
    fields:
      - { label: "Title", name: "title", widget: "string", hint: "This will be the main heading of your blog post" }
      - { label: "Category", name: "category", widget: "select", options: ["AI", "Automation", "Development", "Technology", "Tutorial"], default: "Technology" }
      - { label: "Publish Date", name: "date", widget: "datetime", format: "MMMM DD, YYYY" }
      - { label: "Featured Image", name: "featured_image", widget: "image", required: false, hint: "Optional hero image for the blog post" }
      - { label: "Excerpt", name: "excerpt", widget: "text", required: false, hint: "Brief description of the post (auto-generated if left blank)" }
      - { label: "Tags", name: "tags", widget: "list", required: false, hint: "Add relevant tags for the post" }
      - { label: "Body", name: "body", widget: "markdown", hint: "The main content of your blog post" }
      - { label: "Draft", name: "draft", widget: "boolean", default: false, hint: "Check to save as draft" }
