<!doctype html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Content Manager - Froyolabs Blog</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Check if we're in development mode -->
    <script>
      // Simple check for development environment
      if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        console.log('Development mode detected - Netlify CMS will work with local backend');
      }
    </script>
    
    <!-- Custom preview styles -->
    <style>
      body {
        background: #1f2937 !important;
        color: #e5e7eb;
        font-family: system-ui, -apple-system, sans-serif;
      }
      
      .nc-previewPane-frame {
        background: #1f2937 !important;
      }
      
      /* Style the CMS interface */
      .nc-app-header {
        background: #1f2937 !important;
      }
      
      /* Loading message for development */
      .dev-message {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #374151;
        color: #e5e7eb;
        padding: 2rem;
        border-radius: 0.5rem;
        border: 1px solid #6b7280;
        text-align: center;
        z-index: 1000;
      }
    </style>
  </head>
  <body>
    <div id="dev-message" class="dev-message" style="display: none;">
      <h2>Netlify CMS - Development Mode</h2>
      <p>To use Netlify CMS in development:</p>
      <ol style="text-align: left; margin: 1rem 0;">
        <li>Deploy your site to Netlify</li>
        <li>Enable Netlify Identity</li>
        <li>Enable Git Gateway</li>
        <li>Invite users to manage content</li>
      </ol>
      
      <!-- Add password reset functionality for testing -->
      <div style="margin: 1rem 0; padding: 1rem; background: rgba(255,255,255,0.1); border-radius: 0.5rem; text-align: left;">
        <h3 style="margin: 0 0 0.5rem 0; font-size: 1.1rem; color: #10b981;">🔐 Test Password Reset</h3>
        <p style="margin: 0 0 0.5rem 0; font-size: 0.9rem; opacity: 0.8;">Enter an email to test the password reset functionality:</p>
        <input type="email" id="reset-email" placeholder="Enter email address" style="width: 100%; padding: 0.75rem; margin: 0.5rem 0; border: none; border-radius: 0.25rem; background: white; color: #333; box-sizing: border-box;">
        <div style="display: flex; gap: 0.5rem; align-items: center;">
          <button onclick="requestPasswordReset()" style="padding: 0.75rem 1rem; background: #10b981; color: white; border: none; border-radius: 0.25rem; cursor: pointer; font-weight: 500;">
            Send Reset Email
          </button>
          <button onclick="openResetUI()" style="padding: 0.75rem 1rem; background: #3b82f6; color: white; border: none; border-radius: 0.25rem; cursor: pointer; font-weight: 500;">
            Open Reset UI
          </button>
        </div>
        <div id="reset-status" style="margin-top: 0.75rem; font-size: 0.875rem; padding: 0.5rem; border-radius: 0.25rem; display: none;"></div>
      </div>
      
      <p><strong>In production:</strong> This will work seamlessly once deployed to Netlify.</p>
      <button onclick="document.getElementById('dev-message').style.display='none'" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #7c3aed; color: white; border: none; border-radius: 0.25rem; cursor: pointer;">
        Continue to CMS
      </button>
    </div>

    <!-- Password reset functionality for testing -->
    <script>
      function requestPasswordReset() {
        const email = document.getElementById('reset-email').value;
        const statusEl = document.getElementById('reset-status');
        
        if (!email) {
          showResetStatus('Please enter an email address', 'error');
          return;
        }
        
        if (!window.netlifyIdentity || !window.netlifyIdentity.gotrue) {
          showResetStatus('Netlify Identity widget not loaded', 'error');
          return;
        }

        try {
          showResetStatus('Sending password reset email...', 'info');
          console.log('[password-reset] Sending recovery request for', email);

          // Call GoTrue and log raw response for debugging. The result can be a Response-like object
          // or a plain object depending on how the widget wraps the call.
          window.netlifyIdentity.gotrue.requestPasswordRecovery(email)
            .then(async (res) => {
              try {
                // If this looks like a fetch Response, examine status and body
                if (res && typeof res.status === 'number') {
                  console.log('[password-reset] Raw Response status:', res.status);
                  let bodyText = null;
                  try {
                    const cloned = res.clone ? res.clone() : res;
                    bodyText = await cloned.text();
                    console.log('[password-reset] Raw Response body:', bodyText);
                    try {
                      console.log('[password-reset] Parsed JSON body:', JSON.parse(bodyText));
                    } catch (e) { /* not JSON */ }
                  } catch (e) {
                    console.warn('[password-reset] Could not read response body', e);
                  }

                  if (res.ok) {
                    showResetStatus(`Password reset email sent to ${email}. Check your inbox for the recovery link.`, 'success');
                    console.log('[password-reset] Success: Email sent to', email);
                  } else {
                    // Try to surface a helpful error from the body or status
                    let msg = `Request failed with status ${res.status}`;
                    try {
                      const json = bodyText ? JSON.parse(bodyText) : null;
                      if (json && json.msg) msg = json.msg;
                      else if (json && json.error) msg = json.error;
                    } catch (e) { /* ignore */ }
                    showResetStatus(msg, 'error');
                    console.error('[password-reset] Error response:', res.status, bodyText);
                  }
                } else {
                  // Non-Response result (some wrappers resolve with a simple object)
                  console.log('[password-reset] Got result:', res);
                  showResetStatus(`Password reset email sent to ${email}. Check your inbox for the recovery link.`, 'success');
                }
              } catch (e) {
                console.error('[password-reset] Exception while handling response:', e);
                showResetStatus('Password reset request completed but response parsing failed. See console for details.', 'error');
              }
            })
            .catch((error) => {
              console.error('[password-reset] Error:', error);
              let errorMsg = 'Failed to send reset email';

              if (error && error.message) {
                if (error.message.toLowerCase().includes('user not found')) {
                  errorMsg = 'No user found with this email address. Please check the email or create a new account.';
                } else if (error.message.toLowerCase().includes('rate limit')) {
                  errorMsg = 'Too many requests. Please wait a few minutes before trying again.';
                } else {
                  errorMsg = `Error: ${error.message}`;
                }
              }

              // If the error is a Response-like object, try to log details
              try {
                if (error && typeof error.status === 'number') {
                  console.error('[password-reset] Response-like error status:', error.status);
                  if (error.text) {
                    error.text().then((t) => console.error('[password-reset] Response-like error body:', t)).catch(() => {});
                  }
                }
              } catch (e) { /* ignore */ }

              showResetStatus(errorMsg, 'error');
            });
        } catch (e) {
          console.error('[password-reset] Exception:', e);
          showResetStatus('Failed to request password reset: ' + (e && e.message ? e.message : String(e)), 'error');
        }
      }
      
      function openResetUI() {
        if (window.netlifyIdentity && window.netlifyIdentity.open) {
          try {
            window.netlifyIdentity.open('recovery');
          } catch (e) {
            window.netlifyIdentity.open();
          }
        } else {
          showResetStatus('Netlify Identity widget not available', 'error');
        }
      }
      
      function showResetStatus(message, type) {
        const statusEl = document.getElementById('reset-status');
        statusEl.style.display = 'block';
        statusEl.textContent = message;
        
        // Reset classes
        statusEl.className = '';
        
        if (type === 'success') {
          statusEl.style.background = 'rgba(16, 185, 129, 0.2)';
          statusEl.style.color = '#10b981';
          statusEl.style.border = '1px solid rgba(16, 185, 129, 0.3)';
        } else if (type === 'error') {
          statusEl.style.background = 'rgba(239, 68, 68, 0.2)';
          statusEl.style.color = '#ef4444';
          statusEl.style.border = '1px solid rgba(239, 68, 68, 0.3)';
        } else {
          statusEl.style.background = 'rgba(59, 130, 246, 0.2)';
          statusEl.style.color = '#3b82f6';
          statusEl.style.border = '1px solid rgba(59, 130, 246, 0.3)';
        }
      }
    </script>

    <!-- Helper function for consistent token parsing and validation -->
    <script>
      // Global helper for consistent token parsing across the app
      window.parseNetlifyTokenFromHash = function(hash) {
        if (!hash) return null;
        
        try {
          // Remove leading # and optional /
          const normalized = hash.replace(/^#\/?/, '');
          console.log('[token-parser] Parsing hash:', normalized);
          
          // Try URLSearchParams first
          try {
            const params = new URLSearchParams(normalized);
            const token = params.get('confirmation_token') || params.get('recovery_token') || params.get('token');
            if (token) {
              console.log('[token-parser] Found token via URLSearchParams:', token.substring(0, 20) + '...');
              return { type: params.get('confirmation_token') ? 'confirmation' : params.get('recovery_token') ? 'recovery' : 'generic', token: token };
            }
          } catch (e) {
            console.debug('[token-parser] URLSearchParams failed, trying manual parsing');
          }
          
          // Fallback to manual parsing
          const parts = normalized.split('&');
          for (const part of parts) {
            const [key, value] = part.split('=');
            if ((key === 'confirmation_token' || key === 'recovery_token' || key === 'token') && value) {
              const decoded = decodeURIComponent(value);
              console.log('[token-parser] Found token via manual parsing:', decoded.substring(0, 20) + '...');
              return { type: key === 'confirmation_token' ? 'confirmation' : key === 'recovery_token' ? 'recovery' : 'generic', token: decoded };
            }
          }
        } catch (e) {
          console.error('[token-parser] Error parsing token:', e);
        }
        
        return null;
      };
    </script>

    <!-- Initialize Netlify Identity early so it can handle confirmation/recovery tokens in the URL hash -->
    <script src="https://identity.netlify.com/v1/netlify-identity-widget.js"></script>
    <script>
      (function initNetlifyIdentityForTokens(){
        // If the widget script loaded, initialize it so it can process tokens in the URL hash
        if (window.netlifyIdentity) {
          // When testing locally, point the widget at the deployed site's Identity API
          // so actions that send email (password recovery, confirmation) are performed by Netlify
          const PROD_IDENTITY_API = 'https://froyolabs.netlify.app/.netlify/identity';
          const isLocal = (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');
          const initOptions = isLocal ? { APIUrl: PROD_IDENTITY_API } : {};

          // Helper: show dev instructions if Identity API is missing or returns an error
          function showIdentityUnavailableNotice() {
            const el = document.getElementById('dev-message');
            if (el) {
              el.style.display = 'block';
              el.querySelector('h2').textContent = 'Netlify Identity not available';
              el.querySelector('p').textContent = 'The Netlify Identity API returned an error (404 or unreachable). Make sure Identity is enabled on the deployed site and the API URL is correct.';
            }
            console.warn('[netlify-identity] API not available at', initOptions.APIUrl || 'default');
          }

          // If we have a specific APIUrl (local testing pointing to production site), probe it first to avoid an opaque 404
          if (initOptions.APIUrl) {
            console.log('[netlify-identity] Probing API endpoint:', initOptions.APIUrl);
            // Probe the endpoint to check it exists before initializing the widget
            fetch(initOptions.APIUrl, { method: 'GET', mode: 'cors' })
              .then((res) => {
                if (!res.ok) {
                  console.warn('[netlify-identity] API probe failed with status:', res.status);
                  // show friendly message and avoid calling init which causes the 404 resource fetch
                  showIdentityUnavailableNotice();
                  return;
                }
                console.log('[netlify-identity] API probe successful, initializing widget');

                // If a recovery token is present in the original URL, normalize it BEFORE init and enable recovery mode flag.
                try {
                  const preTokenInfo = window.parseNetlifyTokenFromHash(window.location.hash);
                  if (preTokenInfo && preTokenInfo.type === 'recovery') {
                    const recoveryHash = '#recovery_token=' + encodeURIComponent(preTokenInfo.token);
                    if (window.location.hash !== recoveryHash) {
                      history.replaceState(null, '', window.location.pathname + recoveryHash);
                      console.log('[netlify-identity] Normalized recovery hash BEFORE init');
                    }
                    // Mark that we're in a recovery flow so we can prevent auto-login behavior
                    window.__netlifyRecoveryMode = true;
                  }
                } catch (e) { console.warn('[netlify-identity] Pre-init normalization failed', e); }

                // Attach an early login handler so we catch any auto-login that happens during init
                try {
                  netlifyIdentity.on('login', (user) => {
                    console.log('[netlify-identity] (early) Login event detected for user:', user && user.email);
                    if (window.__netlifyRecoveryMode) {
                      console.log('[netlify-identity] (early) Forcing logout due to recovery flow');
                      try { netlifyIdentity.logout(); } catch (e) { console.warn('[netlify-identity] (early) logout failed', e); }
                      setTimeout(() => {
                        try { netlifyIdentity.open('recovery'); } catch (e) { console.warn('[netlify-identity] (early) reopen recovery failed', e); }
                        window.__netlifyRecoveryMode = false;
                      }, 250);
                    }
                  });
                } catch (e) { console.warn('[netlify-identity] Failed to attach early login handler', e); }
                
                try {
                  netlifyIdentity.init(initOptions);
                  console.log('[netlify-identity] Widget initialized successfully');
                } catch (e) {
                  console.warn('Netlify Identity init failed', e);
                }

                // Parse tokens that may appear in the URL hash (confirmation_token, recovery_token, token)
                const tokenInfo = window.parseNetlifyTokenFromHash(window.location.hash);

                if (tokenInfo && tokenInfo.token) {
                  console.log('[netlify-identity] Processing', tokenInfo.type, 'token:', tokenInfo.token.substring(0, 20) + '...');
                  console.log('[netlify-identity] Using API URL:', initOptions.APIUrl);
                  
                  // Add error handling for authentication failures
                  netlifyIdentity.on('error', (err) => {
                    console.error('[netlify-identity] Error details:', err);
                    console.error('[netlify-identity] Error occurred while processing', tokenInfo.type, 'token');
                    
                    // Show user-friendly error message
                    const errorMsg = err && err.message ? err.message : 'Authentication failed';
                    let userMessage = '';
                    let showDebugOption = false;
                    
                    if (errorMsg.toLowerCase().includes('user not found')) {
                      userMessage = tokenInfo.type === 'recovery' 
                        ? `The email address associated with this recovery link was not found in our system.

This could happen if:
• Your account was removed from our system
• This recovery link is for a different website
• There was an issue with the email delivery

Would you like to:
• Try requesting a new password reset
• Contact support for assistance
• Use the debug tool to investigate further`
                        : 'User not found. Please verify the email address.';
                      showDebugOption = true;
                    } else if (errorMsg.toLowerCase().includes('invalid token') || errorMsg.toLowerCase().includes('expired')) {
                      userMessage = tokenInfo.type === 'recovery'
                        ? 'This password reset link has expired or is invalid. Recovery links are only valid for 1 hour after they are sent.\n\nPlease request a new password reset link.'
                        : 'This link is invalid or has expired. Please request a new confirmation email.';
                    } else if (errorMsg.toLowerCase().includes('token not found')) {
                      userMessage = 'The authentication token is missing or malformed. Please try again with a fresh link.';
                    } else {
                      userMessage = 'Authentication error: ' + errorMsg + '\n\nPlease try requesting a new recovery link.';
                      showDebugOption = true;
                    }
                    
                    if (showDebugOption) {
                      userMessage += '\n\nFor technical debugging, visit: ' + window.location.origin + '/debug-recovery.html#' + window.location.hash.replace(/^#/, '');
                    }
                    
                    alert(userMessage);
                  });

                  // Add success handlers for debugging
                  netlifyIdentity.on('login', (user) => {
                    console.log('[netlify-identity] Login successful for user:', user.email);
                    // If this login happened as part of a recovery token flow, the widget may have auto-logged-in
                    // which prevents showing the password-set form. Force logout and reopen the recovery UI.
                    if (window.__netlifyRecoveryMode) {
                      console.log('[netlify-identity] Detected login during recovery flow — forcing logout and reopening recovery UI');
                      try { netlifyIdentity.logout(); } catch (e) { console.warn('[netlify-identity] logout failed', e); }
                      // Give the logout a short moment then open the recovery modal so the password field is enabled
                      setTimeout(() => {
                        try { netlifyIdentity.open('recovery'); } catch (e) { console.warn('[netlify-identity] reopen recovery failed', e); }
                        // Clear the flag to avoid loops
                        window.__netlifyRecoveryMode = false;
                      }, 250);
                    }
                  });

                  netlifyIdentity.on('signup', (user) => {
                    console.log('[netlify-identity] Signup successful for user:', user.email);
                  });

                  // Ensure the widget opens so it processes the token
                  netlifyIdentity.on('init', user => {
                    console.log('[netlify-identity] Widget initialized, current user:', user ? user.email : 'none');
                    if (!user) {
                      console.log('[netlify-identity] Opening widget for token processing');

                      // If this is a recovery token, explicitly open the recovery UI and normalize the hash
                      try {
                        if (tokenInfo.type === 'recovery') {
                          try {
                            const recoveryHash = '#recovery_token=' + encodeURIComponent(tokenInfo.token);
                            if (window.location.hash !== recoveryHash) {
                              history.replaceState(null, '', window.location.pathname + recoveryHash);
                              console.log('[netlify-identity] Normalized URL hash for recovery token');
                            }
                          } catch (e) {
                            console.warn('[netlify-identity] Failed to normalize recovery hash', e);
                          }

                          try {
                            netlifyIdentity.open('recovery');
                          } catch (e) {
                            console.warn('[netlify-identity] Failed to open recovery UI, falling back to default open()', e);
                            try { netlifyIdentity.open(); } catch (e2) { console.warn('[netlify-identity] open() also failed', e2); }
                          }
                        } else {
                          try { netlifyIdentity.open(); } catch (e) { console.warn('[netlify-identity] Failed to open widget:', e); }
                        }
                      } catch (e) {
                        console.warn('[netlify-identity] Error while opening widget for token processing', e);
                      }
                    }
                  });

                  // Also attempt to open immediately (no-op if already handled)
                  try {
                    if (tokenInfo.type === 'recovery') {
                      // normalize the hash before opening
                      try {
                        const recoveryHash = '#recovery_token=' + encodeURIComponent(tokenInfo.token);
                        if (window.location.hash !== recoveryHash) {
                          history.replaceState(null, '', window.location.pathname + recoveryHash);
                        }
                      } catch (e) { /* ignore */ }

                      try { netlifyIdentity.open('recovery'); } catch (e) { console.warn('[netlify-identity] Failed to open recovery UI immediately:', e); }
                    } else {
                      try { netlifyIdentity.open(); } catch (e) { console.warn('[netlify-identity] Failed to open widget immediately:', e); }
                    }
                  } catch (e) { console.warn('[netlify-identity] Failed to open widget:', e); }
                }
              })
              .catch((err) => {
                // Network error probing API
                console.warn('[netlify-identity] probe failed', err);
                showIdentityUnavailableNotice();
              });
          } else {
            // No custom APIUrl — just init normally (production environment)
            console.log('[netlify-identity] Production environment - initializing with default settings');
            try {
              // If a recovery token is present in the original URL, normalize it BEFORE init.
              try {
                const preTokenInfo = window.parseNetlifyTokenFromHash(window.location.hash);
                if (preTokenInfo && preTokenInfo.type === 'recovery') {
                  const recoveryHash = '#recovery_token=' + encodeURIComponent(preTokenInfo.token);
                  if (window.location.hash !== recoveryHash) {
                    history.replaceState(null, '', window.location.pathname + recoveryHash);
                    console.log('[netlify-identity] Normalized recovery hash BEFORE init (production)');
                  }
                  window.__netlifyRecoveryMode = true;
                }
              } catch (e) { console.warn('[netlify-identity] Pre-init normalization failed (production)', e); }

              // Attach an early login handler in production init path as well
              try {
                netlifyIdentity.on('login', (user) => {
                  console.log('[netlify-identity] (early-prod) Login event detected for user:', user && user.email);
                  if (window.__netlifyRecoveryMode) {
                    console.log('[netlify-identity] (early-prod) Forcing logout due to recovery flow');
                    try { netlifyIdentity.logout(); } catch (e) { console.warn('[netlify-identity] (early-prod) logout failed', e); }
                    setTimeout(() => {
                      try { netlifyIdentity.open('recovery'); } catch (e) { console.warn('[netlify-identity] (early-prod) reopen recovery failed', e); }
                      window.__netlifyRecoveryMode = false;
                    }, 250);
                  }
                });
              } catch (e) { console.warn('[netlify-identity] Failed to attach early login handler (production)', e); }
              
              netlifyIdentity.init(initOptions);
              console.log('[netlify-identity] Production widget initialized successfully');
            } catch (e) {
              console.warn('Netlify Identity init failed', e);
            }

            // Parse tokens that may appear in the URL hash (confirmation_token, recovery_token, token)
            const tokenInfo = window.parseNetlifyTokenFromHash(window.location.hash);

            if (tokenInfo && tokenInfo.token) {
              console.log('[netlify-identity] Production: Processing', tokenInfo.type, 'token:', tokenInfo.token.substring(0, 20) + '...');
              console.log('[netlify-identity] Site URL:', window.location.origin);
              
              // Add error handling for authentication failures
              netlifyIdentity.on('error', (err) => {
                console.error('[netlify-identity] Production error details:', err);
                console.error('[netlify-identity] Site origin:', window.location.origin);
                console.error('[netlify-identity] Token type:', tokenInfo.type);
                
                // Show user-friendly error message
                const errorMsg = err && err.message ? err.message : 'Authentication failed';
                let userMessage = '';
                let showDebugOption = false;
                
                if (errorMsg.toLowerCase().includes('user not found')) {
                  userMessage = tokenInfo.type === 'recovery' 
                    ? `The email address associated with this recovery link was not found in our system.

This could happen if:
• Your account was removed from our system
• This recovery link is for a different website
• The email was sent before the account was fully created

Would you like to:
• Try requesting a new password reset from our login page
• Contact support for assistance

If this continues, you may need to create a new account.`
                    : 'User not found. Please verify the email address.';
                  showDebugOption = true;
                } else if (errorMsg.toLowerCase().includes('invalid token') || errorMsg.toLowerCase().includes('expired')) {
                  userMessage = tokenInfo.type === 'recovery'
                    ? 'This password reset link has expired or is invalid. Recovery links are only valid for 1 hour after they are sent.\n\nPlease request a new password reset link from our login page.'
                    : 'This link is invalid or has expired. Please request a new confirmation email.';
                } else if (errorMsg.toLowerCase().includes('token not found')) {
                  userMessage = 'The authentication token is missing or malformed. Please try again with a fresh link from your email.';
                } else {
                  userMessage = 'Authentication error: ' + errorMsg + '\n\nPlease try requesting a new recovery link from our login page.';
                  showDebugOption = true;
                }
                
                if (showDebugOption) {
                  userMessage += '\n\nFor technical debugging, visit: ' + window.location.origin + '/debug-recovery.html#' + window.location.hash.replace(/^#/, '');
                }
                
                alert(userMessage);
              });

              // Add success handlers for debugging
              netlifyIdentity.on('login', (user) => {
                console.log('[netlify-identity] Production login successful for user:', user.email);
                if (window.__netlifyRecoveryMode) {
                  console.log('[netlify-identity] Detected production login during recovery flow — forcing logout and reopening recovery UI');
                  try { netlifyIdentity.logout(); } catch (e) { console.warn('[netlify-identity] logout failed (production)', e); }
                  setTimeout(() => {
                    try { netlifyIdentity.open('recovery'); } catch (e) { console.warn('[netlify-identity] reopen recovery failed (production)', e); }
                    window.__netlifyRecoveryMode = false;
                  }, 250);
                }
              });

              netlifyIdentity.on('signup', (user) => {
                console.log('[netlify-identity] Production signup successful for user:', user.email);
              });

              netlifyIdentity.on('init', user => {
                console.log('[netlify-identity] Production widget initialized, current user:', user ? user.email : 'none');
                if (!user) {
                  console.log('[netlify-identity] Production: Opening widget for token processing');
                  netlifyIdentity.open();
                }
              });

              try { netlifyIdentity.open(); } catch (e) { console.warn('[netlify-identity] Production: Failed to open widget:', e); }
            }
          }
        } else {
          console.warn('Netlify Identity widget script did not load.');
          // Show dev message so the user knows why recovery flows will fail
          const el = document.getElementById('dev-message');
          if (el) {
            el.style.display = 'block';
            el.querySelector('h2').textContent = 'Netlify Identity not loaded';
            el.querySelector('p').textContent = 'The Netlify Identity widget failed to load. This is required for authentication and password recovery.';
          }
        }
      })();
      
      // Add debug information about current user and identity status
      (function debugIdentityStatus() {
        setTimeout(() => {
          if (window.netlifyIdentity) {
            const currentUser = netlifyIdentity.currentUser();
            console.log('[debug] Current user:', currentUser ? currentUser.email : 'Not logged in');
            console.log('[debug] Identity API URL:', netlifyIdentity.gotrue?.api?.APIUrl || 'Default');
            console.log('[debug] Site URL:', window.location.origin);
            
            // Check if we can access the gotrue API
            if (netlifyIdentity.gotrue) {
              console.log('[debug] GoTrue API available');
              console.log('[debug] GoTrue settings:', netlifyIdentity.gotrue.api.settings);
            } else {
              console.warn('[debug] GoTrue API not available');
            }
          } else {
            console.warn('[debug] Netlify Identity widget not available');
          }
        }, 1000);
      })();
    </script>

    <!-- Load React UMD builds so preview templates can use React.createElement -->
    <script src="https://unpkg.com/react@17/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@17/umd/react-dom.production.min.js"></script>

    <!-- Load Netlify CMS after the document body so it can safely append DOM nodes -->
    <script src="https://unpkg.com/netlify-cms@^2.0.0/dist/netlify-cms.js"></script>

    <script>
      // Show development message if in local environment
      if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        document.getElementById('dev-message').style.display = 'block';
        
        // Hide message after 5 seconds automatically
        setTimeout(() => {
          document.getElementById('dev-message').style.display = 'none';
        }, 5000);
      }

      // Wait until CMS is available and DOM is ready, then register preview template
      (function registerPreviewWhenReady() {
        function doRegister() {
          if (window.CMS && typeof window.CMS.registerPreviewTemplate === 'function') {
            CMS.registerPreviewTemplate("blog", ({ entry, widgetFor }) => {
              const title = entry.getIn(["data", "title"]);
              const category = entry.getIn(["data", "category"]);
              const date = entry.getIn(["data", "date"]);
              const body = widgetFor("body");

              return React.createElement("div", {
                style: {
                  backgroundColor: "#1f2937",
                  color: "#e5e7eb",
                  padding: "2rem",
                  fontFamily: "system-ui, -apple-system, sans-serif",
                  lineHeight: "1.6"
                }
              }, [
                React.createElement("div", {
                  key: "category",
                  style: {
                    fontSize: "0.875rem",
                    color: "#a855f7",
                    fontWeight: "600",
                    marginBottom: "0.5rem"
                  }
                }, category),
                React.createElement("h1", {
                  key: "title",
                  style: {
                    fontSize: "2.5rem",
                    fontWeight: "bold",
                    color: "white",
                    marginBottom: "1rem",
                    lineHeight: "1.2"
                  }
                }, title),
                React.createElement("p", {
                  key: "date",
                  style: {
                    color: "#9ca3af",
                    marginBottom: "2rem"
                  }
                }, date && new Date(date).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric"
                })),
                React.createElement("div", {
                  key: "body",
                  style: {
                    color: "#d1d5db"
                  }
                }, body)
              ]);
            });
          } else {
            // Try again shortly until CMS is available
            setTimeout(doRegister, 50);
          }
        }

        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', doRegister);
        } else {
          doRegister();
        }
      })();
    </script>
  </body>
</html>
